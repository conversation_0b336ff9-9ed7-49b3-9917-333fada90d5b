<div
	class="relative-position bp-listing"
	[ngClass]="emptyUserPermission || pendingUserPermission || deniedUserPermission ? 'empty-screen-height' : ''"
>
	<div class="heading p-1.5">
		<div class="header-spacing mx-6">
			<div>
				<h4 class="mb-0 font-bold">User Role</h4>
			</div>
			<div class="text-right">
				<button
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					(click)="openRequestRoleConfirmationModal()"
					eds-id="request-user-role-button"
					[disabled]="!enableRequestBtn || !countriesList.length || loadTableData"
					testId="request-user-role-button"
				>
					{{ loggedInUserRole ? 'Edit Role Settings' : 'Request a User Role' }}
				</button>
			</div>
		</div>
		<eds-tray
			[visible]="showTray"
			heading="Filters"
			[size]="'min'"
			[edsId]="'tray'"
			(edsClose)="onCloseFilterTray()"
			(edsOpen)="onOpenFilterTray()"
		>
			<div slot="content">
				<dx-filter-tray-form
					[sliceName]="'userRole'"
					[resetForm]="resetForm"
					[getFilterData]="getFilterData"
					[userRolesList]="userRolesList"
					[countriesList]="filterCountriesList"
					(selectedFilters)="getSelectedFilters($event)"
				>
				</dx-filter-tray-form>
			</div>
			<div class="action-bar" slot="actions">
				<button gfk-button eds-id="reset-filters-button" testId="reset-filters-button" variant="secondary" (click)="resetFilterTray()">
					Reset Filters
				</button>
				<button gfk-button eds-id="apply-button" testId="apply-button" variant="primary" (click)="confirmApply(1)">Apply</button>
			</div>
		</eds-tray>
	</div>

	<div class="empty-screen-alert" *ngIf="emptyUserPermission && !pendingUserPermission && !deniedUserPermission">
		<div>
			<h3>
				<eds-icon eds-id="dx-bp-lock" icon="remark" size="xl" color="inactive" hovercolor="brand" class="hydrated" id="dx-bp-lock">
				</eds-icon>
				<br /><b>Limited Access</b><br /><br />You currently have read-only access. If you need additional access, please request the
				appropriate user role from the settings.
			</h3>
		</div>
	</div>
	<div class="empty-screen-alert" *ngIf="!emptyUserPermission && pendingUserPermission && !deniedUserPermission">
		<div>
			<h3><b>Approval Pending</b><br /></h3>
			<h4>Your request for a user-role is on its way. Thanks for being patient.</h4>
		</div>
	</div>
	<div class="empty-screen-alert" *ngIf="!emptyUserPermission && !pendingUserPermission && deniedUserPermission">
		<div>
			<h3><b>User Role Request Declined</b></h3>
			<h4>
				Your requested user role request was declined. <br />
				If you want to access, please click the button 'Request a User Role'.<br />
				&nbsp; &nbsp; Your request will then be forwarded to the admin.
			</h4>
		</div>
	</div>

	<div *ngIf="!emptyUserPermission && !pendingUserPermission && !deniedUserPermission">
		<div class="filter-area" *ngIf="showFilterTrayBtn">
			<div>
				<eds-chip label="{{ chip.label }}" class="chips mr-2" *ngFor="let chip of chips">: {{ chip.value }}</eds-chip>
			</div>
			<div [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
				<dx-filter-button
					showFilterCount="{{ filterCount }}"
					(isShownFilterTray)="isShownFilterTrayEventHandler($event)"
					[disableFilterBtn]="loadTableData"
				></dx-filter-button>
			</div>
		</div>

		<div class="mx-6" *ngIf="loggedInUserRole">
			<article class="table-grid mt-5">
				<eds-table
					eds-id="base-project-table"
					hasRowSelect="checkbox"
					[isLoading]="loadTableData"
					(edsRowSelected)="selectRowsWithConditions($event, userRoleData, loggedInUserRole); rowsSelectedHandler($event, userRoleData)"
				>
					<thead slot="thead">
						<eds-tr variant="header">
							<eds-th
								variant="selector"
								[isSelected]="areAllSelectedWithPagination(visibleUserRole)"
								[columnIndex]="1"
								*ngIf="shouldDisplaySelector()"
							>
								Name
							</eds-th>
							<eds-th
								variant=""
								[isSelected]="areAllSelectedWithPagination(visibleUserRole)"
								[columnIndex]="1"
								*ngIf="!shouldDisplaySelector()"
							>
								<span class="pl-9">Name</span>
							</eds-th>
							<eds-th column-index="2">Email</eds-th>
							<eds-th column-index="3">User Role Requested</eds-th>
							<eds-th column-index="4">Status</eds-th>
							<eds-th column-index="5">Authority Person</eds-th>
							<eds-th column-index="6">Countries</eds-th>
							<eds-th column-index="6">Country Status</eds-th>
							<eds-th column-index="7">Last Updated On</eds-th>
						</eds-tr>
					</thead>

					<tbody slot="tbody">
						<ng-container *ngIf="visibleUserRole?.length > 0; else noData">
							<eds-tr *ngFor="let userRole of visibleUserRole; trackBy: trackById" [rowIndex]="userRole?.id">
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant="selector"
									*ngIf="
										loggedInUserRole?.name === 'Master' &&
										!userRole?.selfPermission &&
										(userRole?.status === 'Pending' || userRole?.status === 'Approved')
									"
									>{{ userRole?.firstName + ' ' + userRole?.lastName }}</eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant=""
									*ngIf="
										loggedInUserRole?.name === 'Master' &&
										!userRole?.selfPermission &&
										(userRole?.status === 'Declined' || userRole?.status === 'Revoked')
									"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant=""
									*ngIf="loggedInUserRole?.name === 'Master' && userRole?.selfPermission"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant="selector"
									*ngIf="
										loggedInUserRole?.name === 'Account Manager' &&
										!userRole?.selfPermission &&
										userRole?.roleName === 'MarketData QC' &&
										(userRole?.status === 'Pending' || userRole?.status === 'Approved') &&
										userRole?.countryStatus === 'Pending'
									"
									>{{ userRole?.firstName + ' ' + userRole?.lastName }}</eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant=""
									*ngIf="
										loggedInUserRole?.name === 'Account Manager' && !userRole?.selfPermission && userRole?.roleName === 'Master'
									"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant=""
									*ngIf="
										loggedInUserRole?.name === 'Account Manager' &&
										!userRole?.selfPermission &&
										userRole?.roleName === 'Account Manager'
									"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant=""
									*ngIf="
										loggedInUserRole?.name === 'Account Manager' &&
										!userRole?.selfPermission &&
										userRole?.roleName === 'MarketData QC' &&
										(userRole?.status === 'Declined' || userRole?.status === 'Revoked')
									"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant=""
									*ngIf="
										loggedInUserRole?.name === 'Account Manager' &&
										!userRole?.selfPermission &&
										userRole?.roleName === 'MarketData QC' &&
										userRole?.status === 'Approved' &&
										(userRole?.countryStatus === 'Approved' || userRole?.countryStatus === 'Declined')
									"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant=""
									*ngIf="
										loggedInUserRole?.name === 'Account Manager' &&
										!userRole?.selfPermission &&
										userRole?.roleName === 'MarketData QC' &&
										userRole?.status === 'Pending' &&
										userRole?.countryStatus === 'Declined'
									"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>
								<eds-td
									[isSelected]="isSelected(userRole.id)"
									variant=""
									*ngIf="loggedInUserRole?.name === 'Account Manager' && userRole?.selfPermission"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>
								<eds-td [isSelected]="isSelected(userRole.id)" variant="" *ngIf="loggedInUserRole?.name === 'MarketData QC'"
									><span class="pl-9">{{ userRole?.firstName + ' ' + userRole?.lastName }}</span></eds-td
								>

								<eds-td>{{ userRole?.userEmail }}</eds-td>
								<eds-td>{{ userRole?.roleName }}</eds-td>
								<eds-td>{{ userRole?.status }}</eds-td>
								<eds-td>{{ userRole?.authorityPerson }}</eds-td>
								<eds-td>
									<span
										class="text-brand cursor-pointer"
										(click)="openAssignedCountriesDetailModal(userRole.countries, userRole.id)"
									>
										{{
											userRole?.selectedCountries?.includes(',')
												? userRole?.selectedCountries?.split(',')[0]
												: userRole?.selectedCountries
										}}

										<gfk-badge variant="soft-action" *ngIf="userRole?.selectedCountries?.includes(',')">
											<span [gfk-tooltip]="userRole?.selectedCountries">+{{ +userRole.countries.length - 1 }}</span>
										</gfk-badge>
									</span>
								</eds-td>
								<eds-td>{{ userRole?.countryStatus }}
                                    <gfk-badge variant="soft-action" *ngIf="userRole?.countryStatus==='Approved'">
                                        <span [gfk-tooltip]="userRole?.approvedCountries">{{userRole?.approvedCountryCount}}</span>
                                    </gfk-badge>
                                    <gfk-badge variant="soft-action" *ngIf="userRole?.countryStatus==='Pending'">
                                        <span [gfk-tooltip]="userRole?.pendingCountries">{{userRole?.pendingCountryCount}}</span>
                                    </gfk-badge>
								</eds-td>
                                <eds-td>{{ userRole?.updatedOn }}</eds-td>
							</eds-tr>
						</ng-container>
						<ng-template #noData>
							<eds-tr
								><eds-td colspan="6">{{
									filterApplied ? 'No records match the applied filter(s)' : 'Open the Filter to see User Roles.'
								}}</eds-td></eds-tr
							>
						</ng-template>
					</tbody>
				</eds-table>

				<gfk-pagination
					*ngIf="userRoleData?.length > 0 && userRoleData?.length > defaultPageSize"
					id="pagination-create-ld"
					[itemsPerPageOptions]="pageSizeOptions"
					[totalCount]="userRoleData?.length"
					[position]="'right'"
					[showItemsPerPage]="true"
					[showFirstAndLast]="true"
					[defaultPageSize]="defaultPageSize"
					[currentPage]="currentPage"
					(onPage)="onPageChangeUserRoles($event)"
				>
				</gfk-pagination>
			</article>
		</div>
		<div>
			<div *ngIf="hasSelection">
				<div class="h-32"></div>
				<div class="selected-user-role-footer">
					<div>
						{{ selectedCount + ' User Role(s) Selected' }}
					</div>
					<div>
						<button
							*ngIf="loggedInUserRole.name === 'Master'"
							gfk-button
							type="primary"
							class="jira-button-margin-right"
							[ngClass]="disableRevokeBtn ? '' : 'transparent-background-theme-btn'"
							(click)="openRevokeConfirmationModal()"
							eds-id="edit-request-button"
							testId="edit-request-button"
							[disabled]="disableRevokeBtn"
						>
							Revoke Access
						</button>
						<button
							gfk-button
							type="primary"
							class="jira-button-margin-right btn-secondary gfk-btn"
							(click)="openEditConfirmationModal()"
							eds-id="accept-request-button"
							testId="accept-request-button"
							[disabled]="disableEditBtn"
						>
							Manage Requests
						</button>
						<button
							gfk-button
							type="secondary"
							class="jira-button-margin-right"
							[ngClass]="disableActionBtn ? '' : 'transparent-background-theme-btn'"
							(click)="openDeclineConfirmationModal()"
							eds-id="decline-request-button"
							testId="decline-request-button"
							[disabled]="disableActionBtn"
						>
							Decline Request
						</button>
						<button
							gfk-button
							type="primary"
							class="jira-button-margin-right btn-secondary gfk-btn"
							(click)="openAcceptConfirmationModal()"
							eds-id="accept-request-button"
							testId="accept-request-button"
							[disabled]="disableActionBtn"
						>
							Accept Request
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- Accept Request Modal -->
<gfk-modal
	[triggerModal]="requestRoleConfirmationModal"
	modalTitle="Request a User Role"
	width="350px"
	cancelTitle="Cancel"
	confirmTitle="Submit"
	[confirmDisabled]="(MIPSelected && disableAddBtn) || disableAddBtn"
	(onAction)="requestUserRoles($event)"
>
	<form [formGroup]="userRoleFormGroup" class="user-role-options">
		<gfk-radio-buttons
			formControlName="userRole"
			(onChange)="countryChangeEvent($event)"
			[options]="userRoleOptions"
			[labelGroup]="userRoleLabelGroup"
			name="userRole"
			[isInline]="false"
			required="false"
			eds-id="userRole"
		>
		</gfk-radio-buttons>

		<div>
			<h5 class="mb-0 font-bold" *ngIf="MIPSelected">Country</h5>
		</div>
		<eds-select
			*ngIf="MIPSelected"
			formControlName="RoleCountry"
			name="RoleCountry"
			eds-id="RoleCountry"
			[labelGroup]="CountryLabelGroup"
			[options]="uniquePendingCountries.length ? uniquePendingCountries : countriesList"
			[required]="false"
			[multiple]="true"
			(edsChange)="checkValues($event)"
			[placeholder]="'Search by Country Name or ID'"
		>
		</eds-select>
		<div class="flex items-center absolute" [ngClass]="MIPSelected ? 'option-one-icon-position-MI' : 'option-one-icon-position'">
			<span [gfk-tooltip]="optionOneHoverText" tooltipDirection="right" tooltipVariant="dark" class="mr-2">
				<eds-icon eds-id="dx-qc-period-info" size="md" icon="info_outline" color="default"> </eds-icon>
			</span>
		</div>
		<div class="flex items-center absolute" [ngClass]="MIPSelected ? 'option-two-icon-position-MI' : 'option-two-icon-position'">
			<span [gfk-tooltip]="optionTwoHoverText" tooltipDirection="right" tooltipVariant="dark" class="mr-2">
				<eds-icon eds-id="dx-qc-period-info" size="md" icon="info_outline" color="default"> </eds-icon>
			</span>
		</div>
		<div class="flex items-center absolute" [ngClass]="MIPSelected ? 'option-three-icon-position-MI' : 'option-three-icon-position'">
			<span [gfk-tooltip]="optionThreeHoverText" tooltipDirection="right" tooltipVariant="dark" class="mr-2">
				<eds-icon eds-id="dx-qc-period-info" size="md" icon="info_outline" color="default"> </eds-icon>
			</span>
		</div>
	</form>
</gfk-modal>
<!-- Edit Request Modal -->
<gfk-modal
	[triggerModal]="editConfirmationModal"
	modalTitle="Manage User Access"
	confirmTitle="Accept"
	(onAction)="editSelectedUserRoles($event)"
	[confirmDisabled]="disableAcceptBtn"
>
	<form [formGroup]="countryGroup">
		<label class="leading-relaxed font-bold text-md h-6 items-center gap-1"> Country </label>
		<eds-select
			formControlName="Country"
			name="Country"
			eds-id="Country"
			[labelGroup]="CountryLabelGroup"
			[options]="filteredCountryList"
			[required]="false"
			[multiple]="true"
			[value]="selectedItem?.countryId"
			(edsChange)="checkAcceptValues($event)"
			[placeholder]="'Search by country name or ID'"
		>
		</eds-select>
	</form>
</gfk-modal>
<!-- Accept Request Modal -->
<gfk-modal
	[triggerModal]="acceptConfirmationModal"
	modalTitle="Confirm the change in user group"
	cancelTitle="Cancel"
	confirmTitle="Submit"
	(onAction)="acceptSelectedUserRoles($event)"
>
	<p>The selected user(s) will change their user roles as specified.</p>
	<p>Would you like to proceed?</p>
</gfk-modal>
<!-- Decline Request Modal -->
<gfk-modal
	[triggerModal]="declineConfirmationModal"
	modalTitle="Confirm the rejection in user group"
	cancelTitle="Cancel"
	confirmTitle="Submit"
	(onAction)="declineSelectedUserRoles($event)"
>
	<p>The selected user(s) will <b>not</b> change their user roles.</p>
	<p>Would you like to proceed?</p>
</gfk-modal>
<!-- Revoke Request Modal -->
<gfk-modal
	[triggerModal]="revokeConfirmationModal"
	modalTitle="Revoke user access"
	cancelTitle="Cancel"
	confirmTitle="Submit"
	(onAction)="revokeSelectedUserRoles($event)"
>
	<p>
		You are going to remove selected users(s) permissions which will leave them with read-only access of the application. This cannot be undone.
	</p>
	<p>Are you sure, you want to revoke user(s) access?</p>
</gfk-modal>
<!-- Assigned Countries Detail Modal -->
<gfk-modal
	[triggerModal]="assignedCountriesDetailModal"
	modalTitle="Assigned Countries Details"
	cancelTitle="Cancel"
	confirmTitle=""
	(onAction)="closeAssignedCountriesDetailModal()">
	<div class="mx-6">
		<article class="table-grid mt-5">
			<eds-table eds-id="base-project-table">
				<thead slot="thead">
					<eds-tr variant="header">
						<eds-th column-index="1">Name</eds-th>
						<eds-th column-index="2">Status</eds-th>
						<eds-th column-index="3">Authority Person</eds-th>
						<eds-th column-index="4">Last Updated On</eds-th>
						<eds-th column-index="5">Action</eds-th>
					</eds-tr>
				</thead>
				<tbody slot="tbody">
					<eds-tr *ngFor="let country of visibleCountryList; trackBy: trackById" [rowIndex]="country?.countryId">
						<eds-td>{{ country?.name }}</eds-td>
						<eds-td>{{ country?.status }}</eds-td>
						<eds-td>{{ country?.authorityPerson }}</eds-td>
						<eds-td>{{ country?.lastUpdatedOn }}</eds-td>
						<eds-td>
							<button
								gfk-button
								type="button"
								class="btn-secondary gfk-btn"
								[ngClass]="'transparent-background-delete-btn'"
								*ngIf="country?.status === 'Approved' && loggedInUserRole?.name === 'Master'"
								testId="revoke-country-button"
								(click)="revokeCountry(countryListDataId, country?.countryId)">
								<eds-icon size="sm" icon="warning" class="inline-flex fill-brand stroke-brand h-5 w-5 hydrated" color="brand">
								</eds-icon>
								Revoke
							</button>
							<span *ngIf="country?.status !== 'Approved' || loggedInUserRole?.name !== 'Master'">N/A</span>
						</eds-td>
					</eds-tr>
				</tbody>
			</eds-table>

			<gfk-pagination
				*ngIf="countryListData?.length > 0 && countryListData?.length > defaultPageSizeforCountryList"
				id="pagination-create-ld"
				[itemsPerPageOptions]="pageSizeOptionsForCountryList"
				[totalCount]="countryListData?.length"
				[position]="'right'"
				[showItemsPerPage]="true"
				[showFirstAndLast]="true"
				[defaultPageSize]="defaultPageSizeforCountryList"
				[currentPage]="currentPageForCountryList"
				(onPage)="onPageChangeCountryList($event)"
			>
			</gfk-pagination>
		</article>
	</div>
</gfk-modal>
