import { Component, HostListener, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { ConfigService } from '@builder/shared/services/config.service';
import { Subject } from 'rxjs';
import { NavigationEnd, Router } from '@angular/router';
import { BannerService } from '@builder/shared/services/banner.service';

interface ICard {
	title: string;
	description: string;
  url: string;
	tooltip:string;
	disableEnableButton:boolean;
}

@Component({
  selector: 'dx-home-page',
  templateUrl: './home-page.component.html',
  styleUrls: ['./home-page.component.scss'],
})
export class HomePageComponent implements OnInit, OnDestroy {
  
  private readonly bannerService = inject(BannerService);
  LandingPageURL = "/https://home.de.vpc1445.in.gfk.com";
  TestApp = 'Builder X';
  withProfile = true;
  withSection = false;
  version?: string;
  currentTab = window.location.href.split('/')[3];
  loginDisplay = false;

  private readonly _destroying$ = new Subject<void>();
  event$ 
  hideBanner: any;
  isBannerVisible!: boolean;
  displayUI = false;
  showContent!: boolean;
  private config = window.CONFIG;
  underMaintainance: boolean = this.config.get('UNDER_MAINTAINANCE') === 'true';

  constructor(
    private configService: ConfigService, 
    public router: Router
  ) {
    const packageVersion = this.configService.getVersion();
    this.version = packageVersion.substring(packageVersion.indexOf('_') + 1);
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.getMarginTop(0);
  }


  tabActive(activetab: string) {
    if (this.currentTab == activetab) {
      this.currentTab = '';
    } else {
      this.currentTab = activetab;
    }
  }

  getMarginTop(number) {
    return number + 25 +'px';
  }

  ngOnInit(): void {
    this.router.events.subscribe((e) => {
			if (e instanceof NavigationEnd) {
			  if(!e.url.includes('redirect-web')){
				this.showContent = true;
			  }
			}
		});
    setTimeout(() => {
      this.displayUI = true;
    }, 1000);
    this.bannerService.isVisible$.subscribe((response: any) => {
      this.isBannerVisible = response;
    });
    this.tabActive(this.currentTab);
  }

  checkAndSetActiveAccount() {
    /**
     * If no active account set but there are accounts signed in, sets first account to active account
     * To use active account set here, subscribe to inProgress$ first in your component
     * Note: Basic usage demonstrated. Your app may require more complicated account selection logic
     */
    // const activeAccount = this.authService.instance.getActiveAccount();
    // let accounts;

    // if (!activeAccount && this.authService.instance.getAllAccounts().length > 0) {
    //   accounts = this.authService.instance.getAllAccounts();
    //   this.authService.instance.setActiveAccount(accounts[0]);
    // }
    // return activeAccount || (accounts ? accounts[0] : null);
  }

  logout() {
    // this.authService.logoutRedirect();
  }

  ngOnDestroy(): void {
    this._destroying$.next(undefined);
    this._destroying$.complete();
  }

}
