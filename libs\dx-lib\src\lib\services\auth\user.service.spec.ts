import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';
import { AuthenticationService } from './authentication.service';
import { UserService } from './user.service';
import { Router } from '@angular/router';
// Define a mock implementation for BaseApi
class MockBaseApi {
	BFF = 'mocked_bff_url';
  }
describe('UserService', () => {
  let userService: UserService;
  let http: HttpClient;
  let authService: AuthenticationService;
  let router: Router;

  beforeEach(() => {
    http = {
      get: jest.fn(),
    } as any;
    authService = {
      signOut: jest.fn(),
    } as any;
    router = {
      navigate: jest.fn(),
    } as any;
	const mockBaseApi = new MockBaseApi(); // Create a mock instance
    userService = new UserService(http, authService, mockBaseApi , router);
  });

  describe('user', () => {
    it(`should return user when user is logged in`, (done) => {
      const user = {
        userId: 1,
        userName: '<PERSON><PERSON><PERSON>',
        firstName: '<PERSON>',
        lastName: 'Doe',
        email: '<EMAIL>',
        roles: [],
        allowedApps: {},
      };
      (http.get as jest.Mock).mockReturnValue(of(user));

      userService.user$.subscribe((receivedUser) => {
        expect(receivedUser).toEqual(user);
        done();
      });

      userService.signInUser();
    });

    it(`should return no user when user signed out again`, (done) => {
      const user = {
        userId: 1,
        userName: 'JohnDoe',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        roles: [],
        allowedApps: {},
      };
      let counter = 0;
      (http.get as jest.Mock).mockReturnValue(of(user));
      (authService.signOut as jest.Mock).mockReturnValue(of({}));

      userService.user$.subscribe((receivedUser) => {
        if (counter === 0) {
          expect(receivedUser).toEqual(user);
          counter += 1;

          userService.signOut().subscribe();
        } else if (counter === 1) {
          expect(receivedUser).toBeUndefined();
          done();
        }
      });

      userService.signInUser();
    });
  });

  // Additional tests for FTX Access can be written similarly.
});
