<form [formGroup]="filterFG">
	<!-- Base Project -->
	<ng-template [ngIf]="sliceName === 'baseProject'">
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Panel </label>
			</div>
			<eds-select
				formControlName="baseProjectPanel"
				eds-id="baseProjectPanel"
				[options]="panelList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Country </label>
			</div>
			<eds-select
				formControlName="baseProjectCountry"
				eds-id="baseProjectCountry"
				[options]="countriesList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Sector </label>
			</div>
			<eds-select
				formControlName="baseProjectSector"
				eds-id="baseProjectSector"
				[options]="sectorsList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Category </label>
			</div>
			<eds-select
				formControlName="baseProjectCategory"
				eds-id="baseProjectCategory"
				[options]="categoriesList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Product Group </label>
			</div>
			<eds-select
				formControlName="baseProjectDomainProductGroup"
				eds-id="baseProjectDomainProductGroup"
				[options]="domainProductGroupData"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Periodicity </label>
			</div>
			<eds-select
				formControlName="baseProjectPeriodicity"
				eds-id="baseProjectPeriodicity"
				[options]="periodicityData"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Data Type </label>
			</div>
			<eds-select
				formControlName="baseProjectDataType"
				eds-id="baseProjectDataType"
				[options]="dataTypeData"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Purpose </label>
			</div>
			<eds-select
				formControlName="baseProjectPurpose"
				eds-id="baseProjectPurpose"
				[options]="purposeData"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Base Project Type </label>
			</div>
			<eds-select
				formControlName="baseProjectType"
				eds-id="baseProjectType"
				[options]="projectSubTypeList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Users </label>
			</div>
			<eds-select
				formControlName="baseProjectUsers"
				eds-id="baseProjectUsers"
				[options]="baseProjectUserList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Date Range </label>
			</div>
		<eds-select
			formControlName="baseProjectHistory"
			[value]="filtersFromNavigation?.baseProjectHistory"
			eds-id="dateInterval-input"
			[options]="dateIntervalList ? dateIntervalList : []"
			[required]="false"
			[multiple]="false"
			class="date width-100"
			[placeholder]="'Choose from list'">
		</eds-select>
		</div>
		<div class="mt-5">
			<dx-date-range-autocomplete  [selectedHistory]="bpHistory" [isListing]="true" class="filters-align" (selectedDate)="selectedDateFilter($event)">
			</dx-date-range-autocomplete>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Base Project Ids </label>
			</div>
			<eds-input
			formControlName="baseProjectIds"
			gfk-input
			type="text" 
			name="baseProjectId"
			[placeholder]="'Type here to search..'"
			eds-id="baseProjectId"
			[required]="false"
			inputmode="numeric" />
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> QC Project Ids </label>
			</div>
			<eds-input
			formControlName="qcProjectIds"
			gfk-input
			type="text" 
			name="qcProjectId"
			[placeholder]="'Type here to search..'"
			eds-id="qcProjectId"
			[required]="false"
			inputmode="numeric" />
		</div>
        <div class="mt-5">
                <div class="mb-2">
                    <label class="filter-label">
                    <input
                        type="checkbox"
                        formControlName="includeDeletedBaseProjects"
                        class="filter-checkbox"
                    />
                    <eds-icon icon="warning" size="xs" color="brand" hoverColor="default" />
                    View Deleted Base Projects Only
                    </label>
                </div>
            </div>
	</ng-template>

	<!-- User List -->
	<ng-template [ngIf]="sliceName === 'userRole'">
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> E-Mail </label>
			</div>
			<eds-select
				formControlName="email"
				eds-id="email"
				[options]="userEmailList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> User Role Requested </label>
			</div>
			<eds-select
				formControlName="userRoleRequested"
				eds-id="userRoleRequested"
				[options]="userRolesList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Country </label>
			</div>
			<eds-select
				formControlName="userRoleCountry"
				eds-id="userRoleCountry"
				[options]="countriesList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
		<div class="mt-5">
			<div class="mb-2">
				<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1"> Status </label>
			</div>
			<eds-select
				formControlName="status"
				eds-id="status"
				[options]="userStatusList"
				[required]="false"
				[multiple]="true"
				[disabled]="false"
				[placeholder]="'Choose from list'"
			>
			</eds-select>
		</div>
	</ng-template>
</form>
