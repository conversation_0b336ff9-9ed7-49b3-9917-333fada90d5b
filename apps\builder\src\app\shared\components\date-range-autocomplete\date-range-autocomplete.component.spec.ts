import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { DateRangeAutocompleteComponent } from './date-range-autocomplete.component';

describe('DateRangeAutocompleteComponent', () => {
	let component: DateRangeAutocompleteComponent;
	let fixture: ComponentFixture<DateRangeAutocompleteComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [
				DateRangeAutocompleteComponent,
				HttpClientTestingModule, 
				NoopAnimationsModule
			],
		}).compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(DateRangeAutocompleteComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
