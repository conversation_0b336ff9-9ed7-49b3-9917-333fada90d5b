import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppRoutingModule } from '@builder/app-routing.module';
import { AppComponent } from '@builder/app.component';
import { CoreModule } from '@builder/core/core.module';
import { SharedModule } from './shared/shared.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { NgLibModule, EdsNotificationService } from '@gfk/ng-lib';
import { AuthFailedComponent } from './pages/auth-failed/auth-failed.component';
import { LoggedOutComponent } from './pages/logged-out/logged-out.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { DxLibModule } from '@dwh/dx-lib';
import { RedirectWebComponent } from '@dwh/dx-lib/src/lib/components/redirect-web/redirect-web.component';
import { HomePageComponent } from './pages/home-page/home-page.component';
import { LoaderService } from './shared/services/loader.service';
import { LoaderInterceptor } from './shared/interceptor/loader.interceptor';
import { ServiceWorkerModule } from '@angular/service-worker';
import { environment } from '../environments/environment';

@NgModule({
	declarations: [AppComponent, AuthFailedComponent, HomePageComponent, LoggedOutComponent],
	imports: [
		NgLibModule,
		BrowserModule,
		AppRoutingModule,
		CoreModule,
		BrowserAnimationsModule,
		HttpClientModule,
		SharedModule,
		FormsModule,
		ReactiveFormsModule,
		MatToolbarModule,
		MatIconModule,
		MatButtonModule,
		MatCardModule,
		MatFormFieldModule,
		MatBottomSheetModule,
		MatExpansionModule,
		RedirectWebComponent,
		DxLibModule.forRoot({ API_ROUTES: { BFF: '/api' } }),
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: environment.dev,
    }),
	],
	schemas: [
		CUSTOM_ELEMENTS_SCHEMA
	],
	providers: [
		{
			provide: EdsNotificationService,
			useValue: new EdsNotificationService(),
		},
		LoaderService,
    	{
			provide: HTTP_INTERCEPTORS,
			useClass: LoaderInterceptor,
			multi: true
		}
		// {
		// 	provide: HTTP_INTERCEPTORS,
		// 	useClass: GlobalHttpInterceptorService,
		// 	multi: true,
		// }

	],
	bootstrap: [AppComponent],
})
export class AppModule {}
