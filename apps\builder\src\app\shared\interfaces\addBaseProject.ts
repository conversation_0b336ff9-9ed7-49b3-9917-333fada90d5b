export interface addBaseProject {
    id:number;
    name?: string,
    typeId?: number,
    projectSubType?: number,
    isAPCChecksEnabled?: boolean,
    periodicityId?: number,
    predecessorIds?: [],
    productGroupIds?: [],
    resetCorrectionTypeId?: number,
    isAutoLoad?: boolean,
    sqcMode?: number,
    isAutomatedPriceCheck?: boolean,
    dataTypeId?: number,
    purposeId?: number,
    isRelevantForReportingEnabled?: boolean
}
  