import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { PageNotFoundComponent } from './components/page-not-found/page-not-found.component';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTabsModule } from '@angular/material/tabs';
import { RouterModule } from '@angular/router';
import { MatNativeDateModule } from '@angular/material/core';
import { MatListModule } from '@angular/material/list';
import { MatRadioModule } from '@angular/material/radio';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { NgLibModule } from '@gfk/ng-lib';
import { FooterComponent } from '@builder/shared/components/footer/footer.component';
import { HeaderComponent } from '@builder/shared/components/header/header.component';
import { FilterTrayFormComponent } from './components/filter-tray-form/filter-tray-form.component';
import { FilterButtonComponent } from './components/filter-button/filter-button.component';
import { DxLibModule } from '@dwh/dx-lib/src';
import { AlphaBannerComponent } from './components/alpha-banner/alpha-banner.component';
import { CardComponent } from './components/card/card.component';
import { ChipAutocompleteComponent } from './components/chip-autocomplete/chip-autocomplete.component';
import { DateRangeAutocompleteComponent } from './components/date-range-autocomplete/date-range-autocomplete.component';
import { LoaderComponent } from './components/loader/loader.component';

const standaloneComponents = [
	ChipAutocompleteComponent,
	DateRangeAutocompleteComponent
];

const components = [
	PageNotFoundComponent,
	FooterComponent,
	HeaderComponent,
	FilterTrayFormComponent,
	FilterButtonComponent,
	AlphaBannerComponent,
	CardComponent,
	LoaderComponent
];

const modules = [
	CommonModule,
	MatTableModule,
	MatIconModule,
	MatFormFieldModule,
	FormsModule,
	ReactiveFormsModule,
	MatInputModule,
	MatChipsModule,
	MatSelectModule,
	MatAutocompleteModule,
	MatDatepickerModule,
	MatButtonModule,
	MatMenuModule,
	MatDialogModule,
	MatTabsModule,
	RouterModule,
	MatNativeDateModule,
	MatListModule,
	MatRadioModule,
	MatCardModule,
	MatSnackBarModule,
	MatCheckboxModule,
	NgLibModule,
	DxLibModule,
	FormsModule,
	ReactiveFormsModule
];

@NgModule({
    declarations: [...components],
    exports: [...components, ...modules, ...standaloneComponents],
    providers: [DatePipe],
    imports: [...modules, ...standaloneComponents],
	schemas: [
		CUSTOM_ELEMENTS_SCHEMA
	],
})
export class SharedModule {}
