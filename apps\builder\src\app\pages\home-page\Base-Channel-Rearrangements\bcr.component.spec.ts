import { TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { of, throwError } from 'rxjs';
import { BCRComponent } from './bcr.component';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { ActivatedRoute } from '@angular/router';

describe('BCRComponent', () => {
  const getApiServiceMock = {
    getInfoDetailsById: jest.fn().mockReturnValue(of({ data: '{"key": "value"}' })),
  };

  const activatedRouteMock = {
    snapshot: {
      paramMap: {
        get: jest.fn().mockReturnValue('123'), // Mock ID retrieval
      },
    },
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, RouterTestingModule],
      declarations: [BCRComponent],
      providers: [
        { provide: GetApiService, useValue: getApiServiceMock },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
      ],
    }).compileComponents();
  });

  it('should create', () => {
    const fixture = TestBed.createComponent(BCRComponent);
    const component = fixture.componentInstance;
    expect(component).toBeTruthy();
  });


  it('should not fetch BCR details if ID is not present', () => {
    activatedRouteMock.snapshot.paramMap.get.mockReturnValue(null);

    const fixture = TestBed.createComponent(BCRComponent);
    const component = fixture.componentInstance;

    fixture.detectChanges(); // Triggers ngOnInit

    expect(activatedRouteMock.snapshot.paramMap.get).toHaveBeenCalledWith('id');
    expect(getApiServiceMock.getInfoDetailsById).not.toHaveBeenCalled();
    expect(component.bcrData).toBeUndefined();
  });

  it('should return the correct index from trackByIndex', () => {
    const fixture = TestBed.createComponent(BCRComponent);
    const component = fixture.componentInstance;

    const index = component.trackByIndex(3);
    expect(index).toBe(3);
  });
});
