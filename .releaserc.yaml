branches: 
  - main
  
ci: true
debug: true
dryRun: false
tagFormat: '${version}'

plugins:
  - '@semantic-release/release-notes-generator'
  - '@semantic-release/changelog'
  - '@semantic-release/git'
  - '@semantic-release/gitlab'
#  - - "@semantic-release/exec"
#    - publishCmd: "echo ${nextRelease.version} > version.txt"

# Global plugin options (will be passed to all plugins)
preset: 'conventionalcommits'
gitlabUrl: 'https://gitlab.in.gfk.com/' # your gitlab url

# Responsible for verifying conditions necessary to proceed with the release:
# configuration is correct, authentication token are valid, etc...
verifyConditions:
  - '@semantic-release/changelog'
  - '@semantic-release/git'
  - '@semantic-release/gitlab'

# Responsible for determining the type of the next release (major, minor or patch).
# If multiple plugins with a analyzeCommits step are defined, the release type will be
# the highest one among plugins output.
# Look details at: https://github.com/semantic-release/commit-analyzer#configuration
analyzeCommits:
  - path: '@semantic-release/commit-analyzer'

# Responsible for generating the content of the release note.
# If multiple plugins with a generateNotes step are defined,
# the release notes will be the result of the concatenation of each plugin output.
generateNotes:
  - path: '@semantic-release/release-notes-generator'
    writerOpts:
      groupBy: 'type'
      commitGroupsSort: 'title'
      commitsSort: 'header'
    linkCompare: true
    linkReferences: true

# Responsible for preparing the release, for example creating or updating files
# such as package.json, CHANGELOG.md, documentation or compiled assets
# and pushing a commit.
prepare:
  - path: '@semantic-release/changelog'
    changelogTitle: "# Changelog
  
      All notable changes to this project will be documented in this file. Dates are displayed in UTC."
  - path: '@semantic-release/exec'
    prepareCmd: "echo ${nextRelease.version} > apps/builder/src/assets/version.txt"
  - path: '@semantic-release/git'
    message: 'chore(release): version ${nextRelease.version}'
    assets: 
      - 'CHANGELOG.md'
      - 'package.json'
      - 'apps/builder/src/assets/version.txt'
  


# Responsible for publishing the release.
publish:
  - path: '@semantic-release/gitlab'
