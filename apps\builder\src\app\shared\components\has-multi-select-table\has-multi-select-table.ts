import { ChangeDetectorRef, signal, Signal, TrackByFunction } from '@angular/core';
import { CheckboxState } from '@builder/shared/constants/checkbox-states';
import { Optional } from '@builder/shared/interfaces/optional.type';
import { EdsTr } from '@gfk/ng-lib';
import { TableRowSelection } from '@gfk/stencil-core';
import { EntityId } from '../../../../../../../libs/dx-lib/src/lib/interfaces/entity-id';


export abstract class HasMultiSelectTable<T> {
	// protected selected: Map<number, T>;
	protected selected = signal<Map<EntityId, T>>(new Map());
	abstract get allRows(): Signal<readonly EdsTr[]>;

	trackById: TrackByFunction<T> = (_: number, entity: T): number => this.getId(entity);

	get selectedEntities(): T[] {
		return Array.from(this.selected().values());
	}

	get selectedCount(): number {
		return this.selected().size;
	}

	get hasSelection(): boolean {
		return this.selected().size > 0;
	}

	abstract getId(entity: T): any;

	constructor(protected readonly cdRef: ChangeDetectorRef) {
		this.selected.set(new Map());
	}

	isSelected(entityId: number): Optional<boolean> {
		if (this.selected().has(entityId)) return CheckboxState.CHECKED;
		return CheckboxState.UNCHECKED;
	}

	areAllSelected(totalPGs?: number): Optional<boolean> {
		if (this.selected().size === 0) {
			return CheckboxState.UNCHECKED;
		} else if (this.selected().size === totalPGs) {
			return CheckboxState.CHECKED;
		}

		return CheckboxState.INDETERMINATE;
	}

	onSelectAll(event: Event, allEntities: Optional<T[]>) {
		if (!(event instanceof CustomEvent) || !allEntities || allEntities.length === 0) {
			return;
		}
		event.preventDefault();
		event.stopPropagation();
		const { detail } = event as CustomEvent<TableRowSelection>;

		if (detail.isSelected) {
			this.#selectUnselected();
		} else {
			this.#deselectSelected();
		}
	}

	onSelect(event: Event, allEntities: Optional<T[]>) {
		if (!(event instanceof CustomEvent) || !allEntities || allEntities.length === 0) {
			return;
		}
		const { detail } = event as CustomEvent<EntityId[]>;

		this.selected().clear();
		const allSelectedMap = new Map<EntityId, T>();
		detail.forEach((id) => {
			const entity = allEntities.find((entity) => this.getId(entity) === id);
			if (entity) {
				allSelectedMap.set(this.getId(entity), entity);
			}
		});
		this.selected.set(allSelectedMap);
	}

	areAllSelectedWithPagination(visibleEntities: T[]): boolean | null {
		if (visibleEntities.length === 0) return false;
		if (visibleEntities.every((entity) => this.selected().has(this.getId(entity)))) {
			return true;
		} else if (visibleEntities.some((entity) => this.selected().has(this.getId(entity))) || this.hasSelection) {
			return null;
		} else {
			return false;
		}
	}

	protected filterBodyRows(rows: EdsTr[]): EdsTr[] {
		return rows.filter((row) => (row['el'] as HTMLEdsTrElement).variant === 'body' || !(row['el'] as HTMLEdsTrElement).variant);
	}

	protected clearSelection() {
		this.#deselectSelected();
		this.selected().clear();
		this.selected.set(new Map());
	}

	#selectUnselected(): void {
		// This is a workaround to set the selection. We should use the table's API instead, but the table is not ready yet.
		this.allRows()
			?.filter((row) => !row.isSelected)
			.map((row) => row['el'].querySelector<HTMLButtonElement>('eds-checkbox button'))
			.filter((buttonElement) => buttonElement?.getAttribute('aria-checked') === 'false')
			.forEach((buttonElement) => buttonElement?.click());
	}

	#deselectSelected(): void {
		// This is a workaround to clear the selection. We should use the table's API instead, but the table is not ready yet.
		this.allRows()
			?.filter((row) => row.isSelected)
			.map((row) => row['el'].querySelector<HTMLButtonElement>('eds-checkbox button'))
			.filter((buttonElement) => buttonElement?.getAttribute('aria-checked') === 'true')
			.forEach((buttonElement) => buttonElement?.click());
	}
}
