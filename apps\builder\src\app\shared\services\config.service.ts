import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {

  private config = window.CONFIG;
  private apiVersion = this.config.get('apiVersion');

  constructor(private http: HttpClient) {}

  getApiUrl(): string {
    const apiBaseUrl = this.config.get('bxapiBaseUrl');
    return `${apiBaseUrl}/${this.apiVersion}`;
  }

  getCountryApiUrl(): string {
    const countryApiBaseUrl =  this.config.get('countryApiBaseUrl');
    return `${countryApiBaseUrl}/${this.apiVersion}`;
  }

  getPeriodicityApiUrl(): string {
    const periodicityApiBaseUrl =  this.config.get('periodicityApiBaseUrl');
    return `${periodicityApiBaseUrl}/${this.apiVersion}`;
  }

  getLmxApiUrl(): string {
    const lmxApiBaseUrl =  this.config.get('lmxApiBaseUrl');
    return `${lmxApiBaseUrl}/${this.apiVersion}`;
  }

  getUserRoleApiUrl(): string {
    const userRoleApiBaseUrl =  this.config.get('userRoleApiBaseUrl');
    return `${userRoleApiBaseUrl}`;
  }

  getURMApiUrl(): string {
    const urmApiBaseUrl =  this.config.get('urmApiBaseUrl');
    return `${urmApiBaseUrl}/${this.apiVersion}`;
  }

  getQCSecurityApiUrl(): string {
    const qcSecurityApiBaseUrl =  this.config.get('qcSecurityApiBaseUrl');
    return `${qcSecurityApiBaseUrl}`;
  }

  getBCRApiUrl(): string {
    const bcrApiBaseUrl =  this.config.get('bcrApiBaseUrl');
    return `${bcrApiBaseUrl}/${this.apiVersion}`;
  }
  
  getInfoshareApiUrl(): string {
    const infoshareApiBaseUrl =  this.config.get('infoshareApiBaseUrl');
    return `${infoshareApiBaseUrl}/${this.apiVersion}`;
  }

  getProductGroupApiUrl(): string {
    const productGroupApiBaseUrl =  this.config.get('productGroupApiBaseUrl');
    return `${productGroupApiBaseUrl}/${this.apiVersion}`;
  }

  getSignalRApiUrl(): string {
    const bxSignalRApiBaseUrl = this.config.get('bxSignalRApiBaseUrl');
    return `${bxSignalRApiBaseUrl}`;
  }

  getVersion() {
    return this.config.get('version');
  }

  getConfigVariable(): Observable<any> {
		return this.http.get('/assets/config.json'); 
	}
}
