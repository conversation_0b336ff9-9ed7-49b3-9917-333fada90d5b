import { Injectable } from '@angular/core';
import { map, Observable, ReplaySubject, shareReplay } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ConfigService } from './config.service';
import { Country } from '@builder/shared/interfaces/Country';
import { getBaseProjectByBaseProjectId } from '../interfaces/getBaseProjectByBaseProjectId';

@Injectable({
	providedIn: 'root',
})
export class GetApiService {
	apiUrl: string;
	countryUrl: string;
	periodUrl: string;
	lmxUrl: string;
	userRoleUrl: string;
	urmUrl: string;
	qcSecurityUrl: string;
  	infoshareUrl: string;
	private userRoleSubject = new ReplaySubject<any>();
	private bpSecurityUserListSubject = new ReplaySubject<any>();
	static loadTableData: boolean;
	static loadQCPeriodTableData: boolean;
	static loadQCSecurityTableData: boolean;

	constructor(private http: HttpClient, private config: ConfigService) {
		this.apiUrl = `${this.config.getApiUrl()}`;
		this.countryUrl = `${this.config.getCountryApiUrl()}`;
		this.periodUrl = `${this.config.getPeriodicityApiUrl()}`;
		this.lmxUrl = `${this.config.getLmxApiUrl()}`;
		this.userRoleUrl = `${this.config.getUserRoleApiUrl()}`;
		this.urmUrl = `${this.config.getURMApiUrl()}`;
		this.qcSecurityUrl = `${this.config.getQCSecurityApiUrl()}`;
    	this.infoshareUrl = `${this.config.getInfoshareApiUrl()}`;
	}

	setUserRoleSubject(userRole:any) {
		this.userRoleSubject.next(userRole);
	}

	getUserRoleSubject(): Observable<any>{
		return this.userRoleSubject.asObservable();
	}

	setBPSecurityUserListSubject(userRole:any) {
		this.bpSecurityUserListSubject.next(userRole);
	}

	getBPSecurityUserListSubject(): Observable<any>{
		return this.bpSecurityUserListSubject.asObservable();
	}

	getCountries(): Observable<Country[]> {
		return this.http.get<Country[]>(this.countryUrl+'/Countries');
	}

  	getAsyncPeriodicities() {
		return this.http.get(this.periodUrl+'/Periodicities');
	}

  	getAsyncProjectSubType() {
		return this.http.get(this.apiUrl+'/ProjectSubTypes');
	}

	getAsyncBaseProjects() {
		return this.http.get(this.apiUrl+'/BaseProjects/List');
	}

	getlmxApiSectors() {
		return this.http.get(this.lmxUrl+'/Sectors');
	}

	getBaseProjectsByBaseProjectId(baseProjectId: number) {
		return this.http.get<getBaseProjectByBaseProjectId>(this.apiUrl+'/BaseProjects/BaseProject/'+baseProjectId);
	}

	getAsyncPanel() {
		return this.http.get(this.apiUrl+'/baseprojectpanels');
	}

	getAsyncResetCorrectionType() {
		return this.http.get(this.apiUrl+'/resetcorrectiontype');
	}

	getAsyncCurrentPeriodsByPeriodicity(periodicityId: number) {
		return this.http.get(this.periodUrl+'/Periods/current?periodicityId='+periodicityId);
	}

	getQCPeriodByQCProjectID(qcProjectID: number) {
		return this.http.get(this.apiUrl+'/qcperiods/qcperiods/'+qcProjectID);
	}

	getQCPeriodByQCPeriodId(qcPeriodId: number) {
		return this.http.get(this.apiUrl+'/qcperiods/qcperiod/'+qcPeriodId);
	}

	getAsyncPeriodsByPeriodicity(periodicityId: number, limit: number) {
		return this.http.get(this.periodUrl+'/Periods?periodicityId='+periodicityId+'&limit='+limit);
	}

	getAsyncUserEmails() {
		return this.http.get(this.userRoleUrl+'/userroles/useremails');
	}

	getAsyncUserRoles() {
		return this.http.get(this.userRoleUrl+'/roles');
	}

	getAsyncUserStatus() {
		return this.http.get(this.userRoleUrl+'/status');
	}

	getLoggedInUserRole() {
		return this.http.get(this.userRoleUrl+'/userroles/userrole');
	}

	getAsyncDataType() {
		return this.http.get(this.apiUrl+'/baseprojectdatatype');
	}

	getAsyncPurpose() {
		return this.http.get(this.apiUrl+'/baseprojectpurpose');
	}

	getURMUsers(queryParams: { [key: string]: any }) {
		let params = new HttpParams();
		for (const key in queryParams) {
			if (queryParams[key]) {
			  params = params.append(key, queryParams[key]);
			}
		}
		const data = this.http.get(this.urmUrl+'/users/freesearch', { params }).pipe(
						map((users: any) => users.sort(this.sortUsers)),
						shareReplay(1)
					);
		return data;
	}

		getURMUsersfromUsernames(usernames: string[]): Observable<any[]> {
		let params = new HttpParams();
		usernames.forEach((username, index) => {
			params = params.append(`userNames[${index}]`, username);
		});
		return this.http.get<any[]>(this.urmUrl+'/users', { params });
	}

	private sortUsers = (a, b) => a?.lastName?.localeCompare(b?.lastName);

	getAsyncPeriodsByPeriodicityandDate(periodicityId: number,todate:string, limit: number) {
        return this.http.get(this.periodUrl+'/Periods?periodicityId='+periodicityId+'&to='+todate+'&limit='+limit);
    }

	getQCProjectAssignedUsers(qcProjectId: number, projectTypeId: number) {
        return this.http.get(this.qcSecurityUrl + '/projectuser/'+qcProjectId + '/' + projectTypeId);
    }


	getURMUsersByUserId(userIds: number[]) {
		const params = userIds
		.map((id, index) => `userIds[${index}]=${id}`)
		.join('&');
		return this.http.get(this.urmUrl+`/users?${ params }`);
	}

	getUserListForRS() {
		return this.http.get(this.apiUrl+'/retailerseperation/Userlist');
	}

	getInfoDetailsById(id: string): Observable<any> {
		const url = `${this.infoshareUrl}/infodetails/${id}`;
		return this.http.get(url);
	}

	getRetailerSeperationRequestByID(requestID: any) {
        return this.http.get(this.apiUrl+'/retailerseperation/'+requestID);
    }

	getAllBPList(typeId?: any): Observable<any> {
		return this.http.get(this.apiUrl + '/baseprojects/list?typeId='+typeId);
	}

	getAsyncBaseProjectUsers() {
		return this.http.get(this.apiUrl + '/baseprojects/userList');
	}

	getBPAssociations(bpId: number) {
        return this.http.get(this.apiUrl + '/baseprojects/baseProjectAssociations/'+bpId);
    }

}
