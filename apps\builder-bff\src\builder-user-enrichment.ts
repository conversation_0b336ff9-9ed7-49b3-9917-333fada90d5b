import { AccountInfo } from '@azure/msal-node';
import { BaseProxy, Config, DomainProxy, MapProxy, ServiceNames, UrmOptions, UserEnrichment, mainContainer } from '@gfk/bff';
import { injectable } from 'inversify';
import { UrmClient } from '@gfk/bff/src/lib/http-clients';
import { log } from '@gfk/bff/src/lib/logger';
import { UnAuthorizedRequestError } from '@gfk/bff/src/lib/exceptions/unauthorized-request-error';
import { ServiceCommunicationError } from '@gfk/bff/src/lib/exceptions/service-communication-error';
import { AllowedApps, User } from '@gfk/bff/src/lib/auth/user';
import { UserRoleClient } from './user-role/user-role-client';
import { BuilderXServicesNames } from './di/inversion';
import { UserRole } from './user-role/models';

export interface ExtendedUser extends User {
  userRole?: UserRole
}

@injectable()
class BuilderUserEnrichment implements UserEnrichment {
  private urmClient: UrmClient;
  private proxyConfig: (BaseProxy | MapProxy | DomainProxy)[];
  private urmOptions: UrmOptions; 
  private userRoleClient: UserRoleClient; 

  constructor() {
    this.urmClient = mainContainer.get<UrmClient>(ServiceNames.UrmClient);
    this.proxyConfig = mainContainer.get<Config>(ServiceNames.Config).proxy;
    this.urmOptions = mainContainer.get<Config>(ServiceNames.Config).urm;
    this.userRoleClient = mainContainer.get<UserRoleClient>(BuilderXServicesNames.UserRoleClient);
  }

  async getUser(accessToken: string, accountInfo: AccountInfo): Promise<any> {
    if (this.urmOptions.disabled) {
      log.debug({ metaData: { class: 'BuilderUserEnrichment', method: 'getUser' }, message: 'URM is disabled' });
      return Promise.resolve(this.getIdentityUser(accountInfo));
    }

    try {
      log.debug({ metaData: { class: 'BuilderUserEnrichment', method: 'getUser' }, message: accountInfo });

      const starTrackUser = await this.urmClient.getUser(accessToken);

      log.debug({ metaData: { class: 'BuilderUserEnrichment', method: 'getUser' }, message: starTrackUser });

      if (!starTrackUser) throw new UnAuthorizedRequestError('unauthorized user');

      const roles = await this.urmClient.getRoles(accessToken, '*');

      log.debug({ metaData: { class: 'BuilderUserEnrichment', method: 'getUser' }, message: roles });

      const starTrackUserRole = await this.userRoleClient.getUserRole(accountInfo.idTokenClaims.oid);

      const userRole = starTrackUserRole.find((item: any) => item.status == 'Approved');

      const enrichedUser: ExtendedUser = {
        oid: accountInfo.idTokenClaims.oid,
        userId: starTrackUser.userId,
        userName: starTrackUser.userName,
        email: accountInfo.idTokenClaims.emails[0].toLowerCase(),
        firstName: starTrackUser.firstName,
        lastName: starTrackUser.lastName,
        roles,
        allowedApps: this.getAllowedRoutes(this.proxyConfig, roles)
      };

      
      return Promise.resolve(enrichedUser);
    } catch (err) {
      log.error({ message: err });

      if (err instanceof UnAuthorizedRequestError) throw err;
      throw new ServiceCommunicationError(`Internal Communication Error With URM`);
    }
  }

  private getAllowedRoutes(proxyConfig: (BaseProxy | MapProxy | DomainProxy)[], roles: string[]): AllowedApps {
    return proxyConfig.reduce((allowedApps, proxy) => ({ ...allowedApps, ...proxy.getAllowedRoutes(roles) }), {});
  }

  private getIdentityUser = (accountInfo: AccountInfo): ExtendedUser => ({
    oid: accountInfo.idTokenClaims.oid,
    userId: 0,
    userName: '',
    email: accountInfo.idTokenClaims.emails[0].toLowerCase(),
    firstName: '',
    lastName: '',
    roles: [],
    allowedApps: this.getAllowedRoutes(this.proxyConfig, []),
    userRole: null
  });
}

export default BuilderUserEnrichment;