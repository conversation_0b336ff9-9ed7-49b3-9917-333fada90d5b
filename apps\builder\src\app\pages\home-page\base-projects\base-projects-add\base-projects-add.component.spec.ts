import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { BaseProjectsAddComponent } from './base-projects-add.component';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { PutApiService } from '@builder/shared/services/put-api.service';
import { DeleteApiService } from '@builder/shared/services/delete-api.service';

describe('BaseProjectsAddComponent', () => {
  const getApiServiceMock = {
    getAsyncDataType: jest.fn().mockReturnValue(of([])),
    getAsyncPurpose: jest.fn().mockReturnValue(of([])),
    getCountries: jest.fn().mockReturnValue(of([])),
    getAsyncPeriodicities: jest.fn().mockReturnValue(of([])),
    getAsyncProjectSubType: jest.fn().mockReturnValue(of([])),
    getAsyncPanel: jest.fn().mockReturnValue(of([])),
    getBaseProjectsByBaseProjectId: jest.fn().mockReturnValue(of([])),
    getAsyncResetCorrectionType: jest.fn().mockReturnValue(of([])),
    getQCPeriodByQCProjectID: jest.fn().mockReturnValue(of([])),
    getQCPeriodByQCPeriodId: jest.fn().mockReturnValue(of([])),
    getAsyncPeriodsByPeriodicity: jest.fn().mockReturnValue(of([])),
    getAsyncCurrentPeriodsByPeriodicity: jest.fn().mockReturnValue(of([])),
    getBPAssociations:jest.fn().mockReturnValue(of([]))
  };
  
  const postApiServiceMock = {
    getAsyncProductGroup: jest.fn().mockReturnValue(of({})),
    getAsyncBaseProjectsByCountryID: jest.fn().mockReturnValue(of([])),
    getShortDescriptionForBaseProjectName: jest.fn().mockReturnValue(of({})),
    createBaseProject: jest.fn().mockReturnValue(of({})),
    getBaseProjectsListForFilter: jest.fn().mockReturnValue(of({})),
    createQCPeriod: jest.fn().mockReturnValue(of({})),
    createBulkQCPeriod: jest.fn().mockReturnValue(of({}))
  };

  const putApiServiceMock = {
    updateBaseProject: jest.fn().mockReturnValue(of({})),
    updateQCProject: jest.fn().mockReturnValue(of({})),
    updateQCPeriod: jest.fn().mockReturnValue(of({}))
  };

  const deleteApiServiceMock = {
    deleteSelectedBaseProjects: jest.fn().mockReturnValue(of([])),
    deleteSelectedQCPeriods: jest.fn().mockReturnValue(of([]))
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, RouterTestingModule],
      declarations: [BaseProjectsAddComponent],
      providers: [
        FormBuilder,
        { provide: GetApiService, useValue: getApiServiceMock },
        { provide: PostApiService, useValue: postApiServiceMock },
        { provide: PutApiService, useValue: putApiServiceMock },
        { provide: DeleteApiService, useValue: deleteApiServiceMock }
      ],
    }).compileComponents();
  });

  it('should create', () => {
    expect(BaseProjectsAddComponent).toBeTruthy();
  });

  // Add more test cases here...
});

