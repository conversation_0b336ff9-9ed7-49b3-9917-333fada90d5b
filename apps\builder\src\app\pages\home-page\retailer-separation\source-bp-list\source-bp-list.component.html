<div class="relative-position rs-listing">
	<div class="heading p-1.5" *ngIf="jiraID">
		<div class="header-spacing mx-6">
            <div>
				<eds-breadcrumbs>
					<a routerLink="/retailer-separation/list" queryParamsHandling="preserve">Retailer Separation</a>
					<a [routerLink]="'/retailer-separation/list/'+requestID" queryParamsHandling="preserve">{{jiraID}}</a>
				</eds-breadcrumbs>
				<div class="flex items-center">
					<h4 class="mb-0 font-bold">{{jiraID +' |'}}</h4>
					<a class="rs-id-style cursor-pointer text-lg" [href]="(isProdBaseUrl ? 'https://adlm.nielseniq.com' : 'https://adlm-tst.nielseniq.com') + '/jira/browse/'+jiraID" target="_blank" rel="noopener">
						<svg 
							class="mt-2"
							height="20" 
							preserveAspectRatio="xMidYMid" 
							viewBox="0 0 256 256" 
							width="30" 
							xmlns="http://www.w3.org/2000/svg" 
							xmlns:xlink="http://www.w3.org/1999/xlink">
							<linearGradient id="a">
								<stop offset=".18" stop-color="#0052cc"/><stop offset="1" stop-color="#2684ff"/>
							</linearGradient>
							<linearGradient id="b" x1="98.030868%" x2="58.887706%" xlink:href="#a" y1=".1606%" y2="40.765525%"/>
							<linearGradient id="c" x1="100.665247%" x2="55.40181%" xlink:href="#a" y1=".455032%" y2="44.726981%"/>
							<path d="m244.657778 0h-122.951111c0 14.7201046 5.847538 28.837312 16.256224 39.2459977 10.408686 10.4086858 24.525893 16.2562245 39.245998 16.2562245h22.648889v21.8666667c.019613 30.6252661 24.8414 55.4470541 55.466666 55.4666671v-122.1688893c0-5.89103736-4.775629-10.6666667-10.666666-10.6666667z" fill="#2684ff"/>
							<path d="m183.822222 61.2622222h-122.9511109c.0196127 30.6252666 24.8414001 55.4470538 55.4666669 55.4666668h22.648889v21.937778c.039238 30.625256 24.87694 55.431136 55.502222 55.431111v-122.1688891c0-5.8910373-4.775629-10.6666667-10.666667-10.6666667z" fill="url(#b)"/>
							<path d="m122.951111 122.488889h-122.951111c0 30.653031 24.8491913 55.502222 55.5022222 55.502222h22.72v21.866667c.0195448 30.597542 24.7980628 55.407869 55.3955558 55.466666v-122.168888c0-5.891038-4.77563-10.666667-10.666667-10.666667z" fill="url(#c)"/>
						</svg>
					</a>
					<div class="ml-4" *ngIf="retailerSeparationStatus">
						<span class="chip" [ngClass]="
							{
								'pending-state': retailerSeparationStatus === 'Pending',
								'executing-state': retailerSeparationStatus === 'Executing',
								'finished-state': retailerSeparationStatus === 'Finished',
								'edited-state': retailerSeparationStatus === 'Edited',
								'csv-available-state': retailerSeparationStatus === 'CSV Available'
							}">
							{{ retailerSeparationStatus?.toUpperCase() }}
						</span>
					</div>
				</div>
			</div>
			<div class="text-right flex">
				<button
					*ngIf="userRole?.name === 'Master' && retailerSeparationStatus === 'Pending' && sourceBPData?.length > 1"
					[routerLink]="'/retailer-separation/update/'+requestID"
					[queryParams]="{ editall: true }" 
					queryParamsHandling="merge"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					[disabled]="loadTableData"
					eds-id="edit-all-button"
					testId="edit-all-button">
					Edit All
				</button>
				<button
					*ngIf="userRole?.name === 'Master' && retailerSeparationStatus === 'Pending'"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					[ngClass]="loadTableData ? '' : 'transparent-background-decline-btn'"
					(click)="openDeclineConfirmationModal()"
					[disabled]="loadTableData"
					eds-id="decline-button"
					testId="decline-button">
					Decline
				</button>
				<button
					*ngIf="userRole?.name === 'Master' && retailerSeparationStatus === 'Pending'"
					gfk-button
					type="primary"
					class="btn-secondary gfk-btn"
					(click)="openConfirmationModal()"
					[disabled]="loadTableData"
					eds-id="execute-button"
					testId="execute-button">
					Execute
				</button>
				<div 
					*ngIf="userRole?.name === 'Master' && retailerSeparationStatus === 'CSV Available' && !hideDownload"
					class="jira-button-margin-right cursor-pointer"
					(click)="downloadCSV(sourceBPData, jiraID)">
					<eds-icon
						eds-id="dx-download-csv"
						icon="export"
						color="brand"
						size="lg" >
					</eds-icon>
				</div>
				<button
					*ngIf="userRole?.name === 'Master' && (retailerSeparationStatus === 'CSV Available')"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					(click)="openFinishConfirmationModal()"
					[disabled]="loadTableData"
					eds-id="add-base-project-button"
					testId="add-base-project-button">
					Mark Finished
				</button>
			</div>
		</div>
	</div>
	<div class="flex mx-6 mt-5 chip-size" *ngIf="retailerSeparationDetails?.length">
		<div *ngFor="let detail of retailerSeparationDetails">
			<span class="chip jira-button-margin-right" *ngIf="detail?.requestStatus === 'Pending'">
				Requested By:
				<span class="font-bold">
					{{ ' ' + detail?.updatedBy }}
				</span>
			</span>
			<span class="chip jira-button-margin-right" *ngIf="detail?.requestStatus === 'Pending'">
				Requested When:
				<span class="font-bold">
					{{ ' ' + detail?.updatedWhen }}
				</span>
			</span>
			<span class="chip jira-button-margin-right" *ngIf="detail?.requestStatus === 'Edited'">
				Updated By:
				<span class="font-bold">
					{{ ' ' + detail?.updatedBy }}
				</span>
			</span>
			<span class="chip jira-button-margin-right" *ngIf="detail?.requestStatus === 'Edited'">
				Updated When:
				<span class="font-bold">
					{{ ' ' + detail?.updatedWhen }}
				</span>
			</span>
			<span class="chip jira-button-margin-right" *ngIf="detail?.requestStatus === 'Executing'">
				Executed By:
				<span class="font-bold">
					{{ ' ' + detail?.updatedBy }}
				</span>
			</span>
			<span class="chip jira-button-margin-right" *ngIf="detail?.requestStatus === 'Executing'">
				Executed When:
				<span class="font-bold">
					{{ ' ' + detail?.updatedWhen }}
				</span>
			</span>
			<span class="chip jira-button-margin-right" *ngIf="detail?.requestStatus === 'Finished'">
				Finished When:
				<span class="font-bold">
					{{ ' ' + detail?.updatedWhen }}
				</span>
			</span>
		</div>
	</div>
	<div class="mx-6">
		<article class="table-grid mt-5">
			<eds-table eds-id="base-project-table" hasRowSelect="checkbox" [isLoading]="loadTableData" (edsRowSelected)="onSelect($event, sourceBPData)">
				<thead slot="thead">
					<eds-tr variant="header">
						<eds-th variant="" [isSelected]="areAllSelectedWithPagination(visibleSourceBPList)" [columnIndex]="0">
							Source BP ID
						</eds-th>
						<eds-th column-index="1">Retailer BP ID</eds-th>
						<eds-th column-index="2">From Period</eds-th>
						<eds-th column-index="3">To Period</eds-th>
						<eds-th column-index="4">Extrapolation Factor = 1</eds-th>
						<eds-th column-index="5">Reset Corrections</eds-th>
						<eds-th column-index="6">Remarks</eds-th>
					</eds-tr>
				</thead>

				<tbody slot="tbody">
					<ng-container *ngIf="visibleSourceBPList?.length > 0; else noData">
						<eds-tr *ngFor="let sourceBP of visibleSourceBPList; trackBy: trackById" [rowIndex]="sourceBP.id">
							<eds-td>
								<span 
									class="rs-id-style cursor-pointer" 
									[routerLink]="'/retailer-separation/update/'+requestID"
									[queryParams]="setQueryParams(sourceBP.sourceBPId)">
									{{ sourceBP.sourceBPId }}
								</span>
							</eds-td>
							<eds-td>{{ sourceBP.retailerBPId }}</eds-td>
                            <eds-td>{{ sourceBP.fromPeriodId }}</eds-td>
							<eds-td>{{ sourceBP.toPeriodId }}</eds-td>
							<eds-td>{{ sourceBP.extrapolation }}</eds-td>
							<eds-td>{{ sourceBP.resetCorrection }}</eds-td>
                            <eds-td><span class="error-text">{{ sourceBP.isError }}</span></eds-td>
						</eds-tr>
					</ng-container>
					<ng-template #noData>
						<eds-tr><eds-td colspan="6">{{filterApplied ? 'No records match the applied filter(s)' : 'Search or Open the Filter to see Base and QC Projects.'}}</eds-td></eds-tr>
					</ng-template>
				</tbody>
			</eds-table>

			<gfk-pagination
				*ngIf="sourceBPData?.length > 0 && sourceBPData?.length > defaultPageSize"
				id="pagination-create-ld"
				[itemsPerPageOptions]="pageSizeOptions"
				[totalCount]="sourceBPData?.length"
				[position]="'right'"
				[showItemsPerPage]="true"
				[showFirstAndLast]="true"
				[defaultPageSize]="defaultPageSize"
				[currentPage]="currentPage"
				(onPage)="onPageChangeSourceBP($event)">
			</gfk-pagination>
		</article>
	</div>
</div>

<!-- The Modal -->
<gfk-modal
	[triggerModal]="confirmationModal"
	modalTitle="Warning!"
	cancelTitle="Cancel"
	confirmTitle="Execute"
	(onAction)="confirmationModalAction($event, 'execute')">
	<p>
        Are you sure, you want to start EXECUTING retailer separation process for all requests?
	</p>
</gfk-modal>
<!-- The Modal -->

<!-- The Modal -->
<gfk-modal
	[triggerModal]="declineConfirmationModal"
	modalTitle="Warning!"
	cancelTitle="Cancel"
	confirmTitle="Yes, Decline"
	[confirmDisabled]="!declineMessage"
	(onAction)="declineConfirmationModalAction($event)">
	<p>Are you sure, you want to DECLINE all requests?</p>
	<p>Please mention the reason which will be added as a comment in affected Jira tickets.</p>
	<textarea class="w-full textarea-style" placeholder="Type here..." rows="8" maxlength="500" [(ngModel)]="declineMessage"></textarea>
</gfk-modal>
<!-- The Modal -->

<!-- The Modal -->
<gfk-modal
	[triggerModal]="finishConfirmationModal"
	modalTitle="Warning!"
	cancelTitle="Cancel"
	confirmTitle="Finish All"
	(onAction)="finishConfirmationModalAction($event)">
	<p>
        Are you sure, you want to mark all requests not having error to FINISH?
	</p>
</gfk-modal>
<!-- The Modal -->