import { TestBed } from '@angular/core/testing';

import { SignalRService } from './signalR.service'
import { HttpClient, HttpHandler } from '@angular/common/http';

describe('SignalRService', () => {
  let service: SignalRService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        HttpClient,
        HttpHandler
      ]
    });
    service = TestBed.inject(SignalRService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
