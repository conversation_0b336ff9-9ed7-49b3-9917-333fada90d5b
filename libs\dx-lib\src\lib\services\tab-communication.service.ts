import { inject, Ng<PERSON><PERSON> } from '@angular/core';
import { filter, fromEvent, map, Subject } from 'rxjs';
import { runInZone } from '../functions/run-in-zone';
import { TabMessage } from '../interfaces/tab-message';

export abstract class TabCommunicationService<T> {
	protected CHANNEL_NAME = '';
	protected MESSAGE_TYPE = '';
	protected readonly message$ = new Subject<TabMessage<T>>();

	private channel!: BroadcastChannel;
	private readonly EVENT_NAME = 'message';
	private readonly ngZone = inject(NgZone);

	protected initialize() {
		if (!this.CHANNEL_NAME || !this.MESSAGE_TYPE) {
			throw new Error('Broadcastchannel not initialized correctly.');
		}
		this.channel = new BroadcastChannel(this.CHANNEL_NAME);
		this.setUpEventListener();
	}

	protected postMessage(payload: T): void {
		const message: TabMessage<T> = {
			payload,
			type: this.MESSAGE_TYPE,
		};
		this.channel.postMessage(message);
	}

	private isMessageEvent(object: any): object is MessageEvent<T> {
		try {
			return object['data'] && 'type' in object.data && 'payload' in object.data;
		} catch (e) {
			return false;
		}
	}

	private setUpEventListener(): void {
		fromEvent(this.channel, this.EVENT_NAME)
			.pipe(
				runInZone(this.ngZone),
				filter((event: Event) => this.isMessageEvent(event)),
				map((event: Event) => event as unknown as MessageEvent<T>),
				map(({ data }: MessageEvent<T>) => data as TabMessage<T>)
			)
			.subscribe((message) => this.message$.next(message));
	}
}
