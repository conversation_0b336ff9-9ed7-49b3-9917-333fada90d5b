import { Injectable } from '@angular/core';
import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { BehaviorSubject } from 'rxjs';
import { ConfigService } from './config.service';

@Injectable({
    providedIn: 'root'
})
export class SignalRService {
    private hubConnection: HubConnection | null = null;
    private messageSubject = new BehaviorSubject<string>('');
    public message$ = this.messageSubject.asObservable();
    signalRUrl: string;
    
    constructor(private config: ConfigService) {
        this.signalRUrl = `${this.config.getSignalRApiUrl()}`;
    }

    public startConnection(username: string): void {
        const hostname = window.location.hostname;
        const envMatch = hostname.match(/^builder-ui\.(t\d)\.dwh\.in\.gfk\.com$/);
        const isProd = hostname === 'builder.dwh.in.gfk.com';
        let apiHost = '';
        if (envMatch) {
            apiHost = `https://projectservices-api.${envMatch[1]}.dwh.in.gfk.com`;
        } 
        else if (isProd) {
            apiHost = 'https://projectservices-api.dwh.in.gfk.com';
        } 
        else {
            apiHost = 'https://projectservices-api.t1.dwh.in.gfk.com';
        }
        
        this.hubConnection = new HubConnectionBuilder()
        .withUrl(`${apiHost}/notificationHub?username=${encodeURIComponent(username)}`)
        .configureLogging(LogLevel.Information)
        .build();

        this.hubConnection.start()
        .then(() => {
            console.log('SignalR connection established');
        })
        .catch((err) => {
            console.error('SignalR connection failed', err);
        });
    }

    public addMessageListener(): void {
        if (this.hubConnection) {
            this.hubConnection.on('ReceiveNotification', (message: string) => {
                console.log('Message received from server: ', message);
                this.messageSubject.next(message); // Update the message observable
            });
        }
    }

    public sendMessage(message: string): void {
        if (this.hubConnection) {
            this.hubConnection.invoke('SendMessage', message).catch((err) => console.error('Error sending message:', err));
        }
    }

    public stopConnection(): void {
        if (this.hubConnection) {
            this.hubConnection.stop().catch((err) => console.error('Error stopping connection:', err));
        }
    }
}