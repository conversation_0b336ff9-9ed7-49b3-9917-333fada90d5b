export interface BaseProjects {
    baseProjectId: number;
    baseProjectName: string;
    type: string;
    subType: string;
    country: string;
    productGroups: string;
    periodicity: string;
    lastUpdatedOn: Date;
    isRetailerSeparationRequested: boolean;
    includeDeletedBaseProjects: boolean;

  }

export interface BulkCopyBaseProjectResponse {
    baseProjectId: number;
    copiedBaseProjectId: number;
    copiedBaseProjectName?: string;
}
