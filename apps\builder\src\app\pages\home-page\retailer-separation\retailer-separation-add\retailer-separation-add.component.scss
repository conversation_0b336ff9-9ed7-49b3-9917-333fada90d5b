@use 'sass:map' as map;
@use '../../../../../styles/theme/gfk-light.palette.scss' as gfk;
@use '@gfk/style' as gfk-style;

.header-spacing {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.heading {
	border-top: 1px solid rgb(223 226 229);
    border-bottom: 1px solid rgb(223 226 229);
    padding: 10px 0px
}

.chip {
    border: 1px solid #bbc0c9;
    padding: 5px 15px;
    border-radius: 9999px;
}

.pending-state {
    background-color: #EEE78E;
    color: black;
}

.executing-state {
    background-color: #E6735C;
    color: white;
}

.finished-state {
    background-color: #66A586;
    color: white;
}

.edited-state {
    background-color: #D0D2C1;
    color: black;
}

.textarea-style {
    border-radius: 5px;
    resize: none;
    border: 1px solid #bbc0c9;
    padding: 5px;
}

.csv-available-state {
    background-color: #8CB9BD;
    color: white;
}