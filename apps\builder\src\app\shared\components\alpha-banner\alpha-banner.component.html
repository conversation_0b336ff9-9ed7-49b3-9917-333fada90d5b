<gfk-alpha-banner id="dx-alpha-banner">
	<ng-container>
		<h3 class="mb-0 text-white text-2xl">Builder-X MVP</h3>
		<div class="flex items-center px-4">
			<eds-icon
				eds-id="dx-alpha-banner-icon-info"
				icon="help"
				color="white"
				size="xl"
				[gfk-popover]="infoAlpha"
				popoverDirection="bottom-start"
				(click)="onInfoClick($event)"></eds-icon>
			<p class="pl-4 mb-0 text-white max-w-192">
				 This version includes core features, with more features coming in future updates.</p>
		</div>
	</ng-container>
	<ng-container actions>
		<eds-icon eds-id="dx-alpha-banner-icon-close" icon="close" color="white" size="md" (click)="onCloseClick()"></eds-icon>
	</ng-container>
</gfk-alpha-banner>

<gfk-popover #infoAlpha>
	<ul _ngcontent-ng-c3872625929="">
		<li _ngcontent-ng-c3872625929="">Manage Base Projects</li>
		<ul _ngcontent-ng-c3872625929="" class="ml-8"><li _ngcontent-ng-c3872625929="">Base Project Settings</li><li _ngcontent-ng-c3872625929="">Base Project Security</li></ul><li _ngcontent-ng-c3872625929="">Manage QC Projects</li><ul _ngcontent-ng-c3872625929="" class="ml-8"><li _ngcontent-ng-c3872625929="">QC Project Settings</li>
		<li _ngcontent-ng-c3872625929="">QC Project Security</li><li _ngcontent-ng-c3872625929="">QC Periods</li></ul><li _ngcontent-ng-c3872625929="">Retailer Separation</li><li _ngcontent-ng-c3872625929="">User Role Management</li></ul>	 
</gfk-popover>

