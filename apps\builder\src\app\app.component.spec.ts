import { TestBed, ComponentFixture, fakeAsync, tick } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { AuthFacade, UserService } from '@dwh/dx-lib/src/lib/services/auth';
import { Observable, of, throwError, Subject } from 'rxjs';
import { PostApiService } from './shared/services/post-api.service';
import { BannerService } from './shared/services/banner.service';
import { Router, NavigationEnd } from '@angular/router';
import { GetApiService } from './shared/services/get-api.service';
import { SwUpdate } from '@angular/service-worker';
import { SignalRService } from './shared/services/signalR.service';
import { EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';

describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  let authFacade: any;
  let userService: any;
  let postApiService: any;
  let bannerService: any;
  let getApiService: any;
  let signalRService: any;
  let notificationService: any;
  let mockRouter: any;
  let routerEventsSubject: Subject<any>;
  let swUpdate: SwUpdate;

  beforeEach(async () => {
    const userServiceSpy = jest.fn().mockImplementation(() => ({
      signInUser: jest.fn(),
    }));
    const postApiServiceSpy = jest.fn().mockImplementation(() => ({
      getAsyncProductGroup: jest.fn().mockReturnValue(of([])),
    }));
    const bannerServiceSpy = jest.fn().mockImplementation(() => ({
      isVisible$: of(true),
    }));
    const getApiServiceSpy = jest.fn().mockImplementation(() => ({
      getLoggedInUserRole: jest.fn().mockReturnValue(of([{ name: 'User', status: 'Approved' }])),
      setUserRoleSubject: jest.fn(),
    }));
    const signalRServiceSpy = jest.fn().mockImplementation(() => ({
      startConnection: jest.fn(),
      stopConnection: jest.fn(),
      addMessageListener: jest.fn(),
      message$: of('message'),
    }));
    const notificationServiceSpy = jest.fn().mockImplementation(() => ({
      showNotification: jest.fn(),
    }));

    routerEventsSubject = new Subject<any>();

    mockRouter = {
      events: routerEventsSubject.asObservable(),
      navigate: jest.fn(),
    };

    const swUpdateSpy = {
      isEnabled: true,
      available: new Subject<any>(),
    };

    await TestBed.configureTestingModule({
      declarations: [AppComponent],
      imports: [],
      providers: [
        {
          provide: AuthFacade,
          useValue: {
            user$: of({ userName: 'testUser' }),
            registerAuthentication: jest.fn(),
          },
        },
        { provide: UserService, useValue: userServiceSpy() },
        { provide: PostApiService, useValue: postApiServiceSpy() },
        { provide: BannerService, useValue: bannerServiceSpy() },
        { provide: GetApiService, useValue: getApiServiceSpy() },
        { provide: Router, useValue: mockRouter },
        { provide: SwUpdate, useValue: swUpdateSpy },
        { provide: SignalRService, useValue: signalRServiceSpy() },
        { provide: EdsNotificationService, useValue: notificationServiceSpy() },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    authFacade = TestBed.inject(AuthFacade) as any;
    userService = TestBed.inject(UserService) as any;
    postApiService = TestBed.inject(PostApiService) as any;
    bannerService = TestBed.inject(BannerService) as any;
    getApiService = TestBed.inject(GetApiService) as any;
    signalRService = TestBed.inject(SignalRService) as any;
    notificationService = TestBed.inject(EdsNotificationService) as any;
    swUpdate = TestBed.inject(SwUpdate) as any;
  });

  it('should create the app', () => {
    expect(component).toBeTruthy();
  });

  it('should call signInUser and registerAuthentication on init', () => {
    jest.spyOn(component, 'getLoggedInUserRole');
    component.ngOnInit();
    expect(userService.signInUser).toHaveBeenCalled();
    expect(authFacade.registerAuthentication).toHaveBeenCalled();
    expect(component.getLoggedInUserRole).toHaveBeenCalled();
  });

  it('should handle getLoggedInUserRole correctly', () => {
    jest.spyOn(localStorage, 'setItem');
    component.getLoggedInUserRole();
    expect(getApiService.getLoggedInUserRole).toHaveBeenCalled();
    expect(getApiService.setUserRoleSubject).toHaveBeenCalled();
  });

  it('should call getProductGroupList if productGroupData does not exist in localStorage', () => {
    jest.spyOn(component, 'getProductGroupList');
    component.ngOnInit();
  });

  it('should not call getProductGroupList if productGroupData exists in localStorage', () => {
    jest.spyOn(component, 'getProductGroupList');
    component.ngOnInit();
    expect(component.getProductGroupList).not.toHaveBeenCalled();
  });

  it('should handle navigation events and show content', () => {
    component.ngOnInit();
    routerEventsSubject.next(new NavigationEnd(1, '/home', '/home'));
    expect(component.showContent).toBe(true);

    routerEventsSubject.next(new NavigationEnd(1, '/redirect-web', '/redirect-web'));
    expect(component.showContent).toBe(true);
  });

  it('should call signalRService startConnection on init', () => {
    component.ngOnInit();
    expect(signalRService.startConnection).toHaveBeenCalled();
  });

  it('should call signalRService addMessageListener on init', () => {
    component.ngOnInit();
    expect(signalRService.addMessageListener).toHaveBeenCalled();
  });

  it('should call notifyWidget on receiving a message from SignalR', () => {
    jest.spyOn(notificationService, 'showNotification');
  });

  it('should stop SignalR connection on destroy', () => {
    component.ngOnDestroy();
    expect(signalRService.stopConnection).toHaveBeenCalled();
  });
});
