import { ChangeDetectorRef, Component, computed, inject, OnInit, QueryList, ViewChildren } from '@angular/core';
import { HasMultiSelectTable } from '@builder/shared/components/has-multi-select-table/has-multi-select-table';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { EdsTr, EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { filter, map, Observable, pairwise, Subscription } from 'rxjs';
import { PostApiService } from '@builder/shared/services/post-api.service';
import * as moment from 'moment-timezone';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute, NavigationEnd, Router, RoutesRecognized } from '@angular/router';
import { PutApiService } from '@builder/shared/services/put-api.service';
import { SignalRService } from '@builder/shared/services/signalR.service';

export interface PageChange {
	totalPages: number;
	pageIndex: number;
	pageSize: number;
	previousPageIndex: number;
}

export interface AutocompleteOption {
	value: string;
	label: string;
}

@Component({
	selector: 'dx-retailer-separation-list',
	templateUrl: './retailer-separation-list.component.html',
	styleUrls: ['./retailer-separation-list.component.scss'],
})

export class RetailerSeparationListComponent extends HasMultiSelectTable<any> implements OnInit{
	
	@ViewChildren(EdsTr) _allRows!: QueryList<EdsTr>;
	allRows = computed(() => this.filterBodyRows(this._allRows.toArray()));
	@ViewChildren(EdsTr) selectedRows!: QueryList<EdsTr>;
	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);
	subscription!: Subscription;
	userRole: any;
	retailerSeparationData!: any;
	visibleRetailerSeparationList: any[] = [];
	pageSize: any;
	currentPage = 1;
	readonly defaultPageSize = 10;
	readonly pageSizeOptions: number[] = [10, 25, 50, 100];
	statusList: any;
	userList$!: Observable<AutocompleteOption[]>;
	userList!: any;
	filterForm: any;
	filterApplied!: boolean;
	confirmationModal!: boolean;
	retailerSeparationID: any;
	disableExecuteBtn!: boolean;
	disableFinishBtn!: boolean;
	declineMessage!: any
	declineConfirmationModal!: boolean;
	finishConfirmationModal!: boolean;
	loadTableData!: boolean;
	timezone = moment.tz.guess();
	timeZoneAbbreviation!: any;
	isProdBaseUrl!: boolean;

	getId({ id }: any): number {
		return id;
	}

	constructor(
		private getApiService: GetApiService,
		protected cdRef: ChangeDetectorRef,
		private postApiService: PostApiService,
		private formBuilder: FormBuilder,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private putApiService: PutApiService,
		private signalRService: SignalRService
	){
		super(cdRef);
		this.pageSize = this.defaultPageSize;
	}

	ngOnInit(){
		this.subscription = this.getApiService.getUserRoleSubject().subscribe((userRole) => {
			this.userRole = userRole;
		});
		this.filterFormInitialize();
		this.setStatusDropdownValues();
		this.getUserList();
		this.router.events.pipe(filter((e: any) => e instanceof RoutesRecognized),pairwise()).subscribe((e: any) => {
			if(e[0].urlAfterRedirects){
				localStorage.setItem('previousRetailerSeparationUrl', JSON.stringify(e[0].urlAfterRedirects));
			}
		});
		const previousRetailerSeparationUrl = JSON.parse(localStorage.getItem('previousRetailerSeparationUrl')!);
		if(previousRetailerSeparationUrl){
			this.getRetailerSeparationList();
		}
		this.signalRService.message$.subscribe((notificationMessage) => {
			if(notificationMessage){
				this.getRetailerSeparationList();
			}
		});
		const hostname = window.location.hostname;
		this.isProdBaseUrl = hostname === 'builder.dwh.in.gfk.com';
	}

	filterFormInitialize(){
		this.filterForm = this.formBuilder.group({
			jiraIds: null,
			requestStatusIds: [],
			usernames: [],
			startDate: null,
			endDate: null,
		});
		this.filterForm.reset()

        Object.keys(this.filterForm.controls).forEach(key => {
          this.filterForm.controls[key].setErrors(null)
        });
	}

	closeClick() {
		this.filterForm.get('jiraIds')?.setValue('');
	}

	setStatusDropdownValues() {
		this.statusList = [
			{label: 'Pending', value: '1'},
			{label: 'Executing', value: '2'},
			{label: 'Finished', value: '3'},
			{label: 'Error', value: '6'}
		]
	}

	getUserList(){
		this.userList$ = this.getApiService.getUserListForRS().pipe(
			map((userList: any) => userList.map((user: any) => ({ label: user, value: user } as AutocompleteOption)))
		);
		this.userList$.subscribe({
			next: (result) => {
				this.userList = result;
			}
		});
	}	

	selectedDateFilter(value: any) {
		this.filterForm.get('startDate')?.setValue(value.startDate ? moment.utc(value.startDate).tz(this.timezone).startOf('day') : null);
		this.filterForm.get('endDate')?.setValue((value.endDate && new Date(value.endDate).toString() !== "Invalid Date") ? moment.utc(value.endDate).tz(this.timezone).endOf('day') : null);
	}

	checkIfAnyFilterValueExists(payload: any) {
		for (const key in payload) {
		  if (Object.prototype.hasOwnProperty.call(payload, key)) {
			const value = payload[key];
			if (Array.isArray(value)) {
			  if (value.length > 0) {
				return true;
			  }
			} else if (value !== null && value !== undefined) {
				return true;			}
		  }
		}
		return false;
	}

	confirmApply() {
		const hasFilter = this.checkIfAnyFilterValueExists(this.filterForm.value);
		if (hasFilter) {
			this.getRetailerSeparationList();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.getRetailerSeparationList();
			}
		}
	}

	getRetailerSeparationList() {	
		this.loadTableData = true;
		this.postApiService.getRetailerSeparationList(this.filterForm.value).subscribe({
			next: (result: any) => this.handleRetailerSeparationListResponse(result),
			error: (error) => this.handleRetailerSeparationListError(error),
		});
	}

	handleRetailerSeparationListResponse(result: any) {
		if (result.moreRecordsAvailable) {
			const title = 'More than 1000 results exist!';
			this.notifyWidget(title, 'info');
		}
		localStorage.removeItem('previousRetailerSeparationUrl');
		this.retailerSeparationData = [];
		this.visibleRetailerSeparationList = [];
		result.records.forEach((item: any, index: any) => {
			if(item.retailerSeperationRequestDetails.length){
				item.requestDetails = item.retailerSeperationRequestDetails.find((detailItem: any) => detailItem.requestStatus == 'Pending');
				item.isError = item.retailerSeperations.some(separation => separation.isError === true);
				if(item.requestDetails){
					const date = new Date(item.requestDetails.updatedWhen.toString());
					const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, item.requestDetails.updatedWhen);
					const cetTime = moment.utc(date).tz(this.timezone).format('YYYY-MM-DD HH:mm');
					item.requestDetails.updatedWhenCET = cetTime + ' ' + timeZoneAbbreviation;
				}
			}
			else{
				result.records.splice(index, 1);
			}
		});
		this.retailerSeparationData = result.records;
		this.visibleRetailerSeparationList = this.getPageRetailerSeparation(this.currentPage, this.pageSize);
		this.loadTableData = false;
	}

	handleRetailerSeparationListError(error: any) {
		let title: string;
		if (error.status == 404) {
			this.retailerSeparationData = null;
			this.visibleRetailerSeparationList = [];
			const hasFilter = this.checkIfAnyFilterValueExists(this.filterForm.value);
			if(hasFilter){
				this.filterApplied = true;
			}
		} 
		else if ([400, 401, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to fetch Retailer Separation List. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	onPageChangeRetailerSeparation(event: PageChange) {
		this.currentPage = event.pageIndex;
		this.pageSize = event.pageSize;
		this.visibleRetailerSeparationList = this.getPageRetailerSeparation(this.currentPage, this.pageSize);
		this.cdRef.markForCheck();
	}

	private calculatePageStart(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEnd(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

	private getPageRetailerSeparation(page: number, size: number) {
		const start = this.calculatePageStart(page, size);
		const end = this.calculatePageEnd(page, size);
		return this.retailerSeparationData.slice(start, end);
	}

	rowsSelectedHandler(event: any, retailerSeparationData: any){
		const selectedRS: any = [];
		retailerSeparationData.forEach((item: any) => {
			event.detail.forEach((detailItem: any) => {
				if (detailItem == item.id) {
					selectedRS.push(item);
				}
			})
		});
		const allPendingStatus = selectedRS.every((rs: any) => rs.requestStatus === 'Pending');
		const allExecutingStatus = selectedRS.every((rs: any) => (rs.requestStatus === 'Executing' || rs.requestStatus === 'CSV Available'));
		if(allPendingStatus && !allExecutingStatus){
			this.disableExecuteBtn = false;
			this.disableFinishBtn = true;
		}
		else if(!allPendingStatus && allExecutingStatus){
			this.disableExecuteBtn = true;
			this.disableFinishBtn = false;
		}
		else if(!allPendingStatus && !allExecutingStatus){
			this.disableExecuteBtn = true;
			this.disableFinishBtn = true;
		}
	}

	openConfirmationModal() {
		this.confirmationModal = true;
	}

	confirmationModalAction(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.confirmationModal = false;
		}
		else{
			this.loadTableData = true;
			const selectedRSIds = this.getSelectedRetailerSeparationIds();
			this.confirmationModal = false;
			this.postApiService.executeRetailerSeparation({retailerSeperationRequestIds: selectedRSIds}).subscribe({
				next: () => this.handleExecuteResponse(),
				error: (error) => this.handleExecuteError(error),
				complete: () => this.getRetailerSeparationList()
			});
		}
	}

	getSelectedRetailerSeparationIds(): Array<number> {
		return this.selectedEntities.map((item: any) => item.id);
	}

	handleExecuteResponse(){
		this.clearSelection();
		const title = 'Retailer Separation Started';
		const message = "The request is being processed and may take a few minutes. A confirmation notification will be sent once it's complete.";
		this.notifyWidget(title, 'info', message);
		this.loadTableData = false;
	}

	handleExecuteError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to execute Retailer Separation. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	openDeclineConfirmationModal() {
		this.declineConfirmationModal = true;
		this.declineMessage = '';
	}

	declineConfirmationModalAction(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.declineConfirmationModal = false;
		}
		else{
			this.loadTableData = true;
			this.declineConfirmationModal = false;
			const selectedRSIds = this.getSelectedRetailerSeparationIds();
			this.putApiService.updateRetailerSeparationStatus(
				selectedRSIds,
				5,
				this.declineMessage
			).subscribe({
				next: () => this.handleStatusChangeResponse('decline'),
				error: (error) => this.handleStatusChangeError(error),
				complete: () => this.getRetailerSeparationList()
			});
		}
	}

	openFinishConfirmationModal() {
		this.finishConfirmationModal = true;
	}

	finishConfirmationModalAction(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.finishConfirmationModal = false;
		}
		else{
			this.loadTableData = true;
			this.finishConfirmationModal = false;
			const selectedRSIds = this.getSelectedRetailerSeparationIds();
			this.putApiService.updateRetailerSeparationStatus(
				selectedRSIds,
				3,
				''
			).subscribe({
				next: () => this.handleStatusChangeResponse(),
				error: (error) => this.handleStatusChangeError(error),
				complete: () => this.getRetailerSeparationList()
			});
		}
	}

	handleStatusChangeResponse(statusType?: any){
		this.clearSelection();
		const title = statusType == 'decline' ? 'I/R Separation Request Deleted' : 'I/R Request Status Updated';
		this.notifyWidget(title, 'success');
		this.loadTableData = false;
	}

	handleStatusChangeError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to change I/R request status. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	getOffsetAtTime(timezone, date) {
		const zone = moment.tz.zone(timezone);
		if (zone) {
			const timestamp = moment.utc(date).valueOf(); // Convert date to UTC timestamp
			const abbrs = zone.abbrs; // Get the list of abbreviations
			const untils = zone.untils; // Get the list of timestamp changes (for daylight saving time changes)
			
			// Find the correct abbreviation based on the timestamp
			for (let i = 0; i < untils.length; i++) {
				if (timestamp < untils[i]) {
				return abbrs[i]; // Return abbreviation if timestamp is before the DST change
				}
			}
			
			// If no matching change is found (for times after the last DST change), use the last abbreviation
			return abbrs[abbrs.length - 1]; // Return the last abbreviation
		} 
		else {
		  return null; // Return null if the timezone is not found
		}
	}

	notifyWidget(title: string, notificationType: string, message?: string): void {
		if(notificationType == 'info'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if(notificationType == 'error'){
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else{
			this.notificationService.showNotification(title, {
				message: message || '',
				duration : 15000,
				variant: NotificationVariant.SUCCESS,
			});
		}
	}
}