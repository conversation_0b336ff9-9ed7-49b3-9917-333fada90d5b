import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '@builder/shared/shared.module';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { NgLibModule } from '@gfk/ng-lib';
import { DxLibModule } from '@dwh/dx-lib/src';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RetailerSeparationAddComponent } from './retailer-separation-add/retailer-separation-add.component';
import { RetailerSeparationListComponent } from './retailer-separation-list/retailer-separation-list.component';
import { SourceBPListComponent } from './source-bp-list/source-bp-list.component';

const routes: Routes = [
	{
		path: "",
		children: [
			{
				path: "create",
				component: RetailerSeparationAddComponent,
				canActivate: [],
				canDeactivate: []
			},
			{
				path: "list",
				component: RetailerSeparationListComponent,
				canActivate: [],
				canDeactivate: []
			},
			{
				path: "list/:id",
				component: SourceBPListComponent,
				canActivate: [],
				canDeactivate: []
			},
			{
				path: "update/:id",
				component: RetailerSeparationAddComponent,
				canActivate: [],
				canDeactivate: []
			},
		]
	  }
  ];


@NgModule({
	declarations: [
		RetailerSeparationAddComponent,
		RetailerSeparationListComponent,
		SourceBPListComponent
	],
	imports: [
		CommonModule,
		SharedModule,
		RouterModule.forChild(routes),
		MatPaginatorModule,
		MatSortModule,
		NgLibModule,
		DxLibModule,
		FormsModule,
		ReactiveFormsModule
	],
	exports: [RouterModule],
	schemas: [
		CUSTOM_ELEMENTS_SCHEMA
	]
})
export class RetailerSeparationModule {}
