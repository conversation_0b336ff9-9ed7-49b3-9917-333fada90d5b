# Image name: nexus.gfk.com:8471/repository/docker-dwh-hosted/builder-ui
# Build context: ./

# TODO: update this base image
FROM registry-proxy.td.gfk.com/nginxinc/nginx-unprivileged:1.27.1
ARG VERSION=x.y.z
ENV VERSION=$VERSION

# The template needs e var API_URL to be provided during run
COPY --chown=nginx:root ./ci/default.conf.template /etc/nginx/templates/
COPY --chown=nginx:root ./dist/apps/builder /usr/share/nginx/html

# TODO: fix to use the nginxinc entrypoint scripts
COPY --chown=nginx:root ./ci/updateenv.sh /docker-entrypoint.d
RUN chmod +x /docker-entrypoint.d/updateenv.sh

# Run as non-root user
USER root
RUN addgroup --system nonroot \
    && adduser --system --ingroup nonroot nonroot
USER nonroot

# Default nginxinc port and CMD
EXPOSE 8080
CMD ["nginx", "-g", "daemon off;"]
