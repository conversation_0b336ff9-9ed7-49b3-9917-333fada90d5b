import { StorageService } from './storage.service';

describe('StorageService', () => {
	let storageService: StorageService;
	let localStorageMock;

	beforeEach(() => {
		localStorageMock = (() => {
			let store = {};

			return {
				getItem: jest.fn((key) => store[key] || null),
				setItem: jest.fn((key, value) => {
					store[key] = value.toString();
				}),
				removeItem: jest.fn((key) => {
					delete store[key];
				}),
				clear: jest.fn(() => {
					store = {};
				}),
			};
		})();

		Object.defineProperty(window, 'localStorage', {
			value: localStorageMock,
		});
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	beforeEach(() => {
		storageService = new StorageService();
	});

	it('should retrieve null if there is no store value', () => {
		const value = storageService.get('key');

		expect(value).toBeNull();
	});

	it('should retrieve serialized value if there is store value', () => {
		const key = 'key';
		localStorage.setItem(key, '123');

		const value = storageService.get(key);

		expect(localStorage.getItem).toHaveBeenCalledTimes(1);
		expect(localStorage.getItem).toHaveBeenCalledWith(key);
		expect(value).toBe(123);
	});

	it('should set serialized value with the given key', () => {
		const key = 'key';
		const value = 123;

		storageService.set(key, value);

		expect(localStorage.setItem).toHaveBeenCalledTimes(1);
		expect(localStorage.setItem).toHaveBeenCalledWith(key, '123');
	});

	it('should remove value with the given key', () => {
		const key = 'key';
		const value = 123;

		storageService.set(key, value);

		expect(localStorage.setItem).toHaveBeenCalledTimes(1);
		expect(localStorage.setItem).toHaveBeenCalledWith(key, '123');

		storageService.remove(key);

		expect(localStorage.removeItem).toHaveBeenCalledTimes(1);
		expect(localStorage.removeItem).toHaveBeenCalledWith(key);
	});
});
