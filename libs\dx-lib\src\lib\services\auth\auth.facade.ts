import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { AuthorizationInterceptor } from './authorization.interceptor';
import { AuthenticationInterceptor } from './authentication.interceptor';
import { AuthenticationService, SignOutResponse } from './authentication.service';
import { UserService } from './user.service';

export const AUTH_INTERCEPTORS = [
	{
		provide: HTTP_INTERCEPTORS,
		useClass: AuthenticationInterceptor,
		multi: true,
	},
	{
		provide: HTTP_INTERCEPTORS,
		useClass: AuthorizationInterceptor,
		multi: true,
	},
];

@Injectable()
export class AuthFacade {
	isLoading$: Observable<boolean>;
	user$: Observable<any>;

	constructor(private authService: AuthenticationService, private userService: UserService) {
		this.isLoading$ = this.authService.loginInProcess$;
		this.user$ = this.userService.user$;
	}

	registerAuthentication(): void {
		this.authService.registerAuthentication();
	}

	signIn(): void {
		this.userService.signOut().subscribe();
	}

	signOut(): Observable<SignOutResponse> {
		return this.userService.signOut();
	}
}
