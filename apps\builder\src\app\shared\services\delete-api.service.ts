import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ConfigService } from './config.service';

@Injectable({
	providedIn: 'root',
})

export class DeleteApiService {
  apiUrl: string;
  qcSecurityUrl: string;

	constructor(private http: HttpClient, private config: ConfigService) {
    this.apiUrl = `${this.config.getApiUrl()}`;
    this.qcSecurityUrl = `${this.config.getQCSecurityApiUrl()}`;
  }

  deleteSelectedBaseProjects(selectedBaseProjects: any) {
    const baseProjectsIds = {
      headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
      body: selectedBaseProjects
    };
		return this.http.delete(this.apiUrl+'/BaseProjects', baseProjectsIds);
	}

  deleteSelectedQCPeriods(selectedQCPeriods: any) {
    const qcPeriodsIds = {
      headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
      body: selectedQCPeriods
    };
		return this.http.delete(this.apiUrl+'/qcperiods/qcperiods', qcPeriodsIds);
	}

  removeAssignedUsers(payload: any) {
    const data = {
      headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
      body: payload
    };
		return this.http.delete(this.qcSecurityUrl + '/projectuser', data);
	}

  deleteRetailerSeparationRequest(payload: any) {
    const data = {
      headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
      body: payload
    };
		return this.http.delete(this.apiUrl + '/retailerseperation', data);
	}

}
