{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2019", "module": "esnext", "lib": ["es2019", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@builder/*": ["apps/builder/src/app/*"], "@dwh/*": ["libs/*"], "@dwh/dx-lib": ["libs/dx-lib/src/index.ts"]}, "resolveJsonModule": true}, "exclude": ["node_modules", "tmp"]}