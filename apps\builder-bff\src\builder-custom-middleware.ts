import { log, mainContainer } from '@gfk/bff';
import { Application } from 'express';
import { ProblemDetails } from '@gfk/bff/src/lib/problem-details';
import { HttpStatusCode } from '@gfk/bff/src/lib/enums/http-status-code';
import { UserRoleClient } from './user-role/user-role-client';
import { OpaClient } from './opa/opa-client';
import { BuilderXServicesNames } from './di/inversion';

let cachedBaseProjectPanels: any = null;
let cachedProjectSubTypes: any = null;
let cachedCountries: any = null;
let cachedPeriodicities: any = null;

export const builderCustomGfkMiddleware = (app: Application) => {
  app.use((req: any, res: any, next: any) => {
    const originalWrite = res.write.bind(res);
    const originalEnd = res.end.bind(res);
    const chunks: any[] = [];

    res.write = (chunk: any, ...args: any[]) => {
      chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
      return true;
    };

    res.end = (chunk: any, ...args: any[]) => {
      if (chunk) {
        chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
      }

      const rawBody = Buffer.concat(chunks).toString('utf8');
      let modifiedBody = rawBody;

      try {
        if (req.method === 'GET') {
          const json = JSON.parse(rawBody);
          if (req.url.includes('baseprojectpanels')) {
            cachedBaseProjectPanels = json;
          } 
          else if (req.url.includes('ProjectSubTypes')) {
            cachedProjectSubTypes = json;
          } 
          else if (req.url.includes('Countries')) {
            cachedCountries = json;
          } 
          else if (req.url.includes('Periodicities')) {
            cachedPeriodicities = json;
          }
        }

        if (req.method === 'POST' && req.url.includes('/BaseProjects/List')) {
          const parsed = JSON.parse(rawBody);
          if (parsed?.records?.length) {
            const enrichedRecords = parsed.records.map((item: any) => {
              const enriched = { ...item };

              const type = cachedProjectSubTypes?.find((t: any) => t.id === item.typeId);
              if (type) enriched.typeName = type.name;

              const panel = cachedBaseProjectPanels?.find((p: any) => p.id === item.panelId);
              if (panel) enriched.panelName = panel.name;

              const country = cachedCountries?.find((c: any) => c.id === item.countryId);
              if (country) enriched.countryName = country.name;

              const periodicity = cachedPeriodicities?.find((p: any) => p.id === item.periodicityId);
              if (periodicity) enriched.periodicityName = periodicity.name;

              return enriched;
            });

            parsed.records = enrichedRecords;
            modifiedBody = JSON.stringify(parsed);

            console.log('[✅ ENRICHED] BaseProjects/List enriched and ready.');
          }
        }

        // Ensure correct Content-Length
        res.setHeader('Content-Length', Buffer.byteLength(modifiedBody));
        res.setHeader('Content-Type', 'application/json');

      } catch (e) {
        console.error('[❌ Error enriching response]', e);
      }

      return originalEnd(Buffer.from(modifiedBody), ...args);
    };

    next();
  });

  // Authorization check
  app.use('/', async (req: any, res, next) => {
    //Do something for every request
    console.log(req.method, ':', req.url);

    log.debug({ metaData: { class: 'builderCustomGfkMiddleware', method: 'requestMethod' }, message: req.method });

    if(req.session.user){

      const userRoleClient = mainContainer.get<UserRoleClient>(BuilderXServicesNames.UserRoleClient);

      const starTrackUserRole = await userRoleClient.getUserRole(req.session.user.oid);

      const userRole = starTrackUserRole.find((item: any) => item.status == 'Approved');

      log.debug({ metaData: { class: 'builderCustomGfkMiddleware', method: 'starTrackUserRole' }, message: starTrackUserRole });
      log.debug({ metaData: { class: 'builderCustomGfkMiddleware', method: 'userName' }, message: req.session.user });

      const opaClient = mainContainer.get<OpaClient>(BuilderXServicesNames.OpaClient);

      let authApiCheck: any;

      if(userRole){

        authApiCheck = await opaClient.authenticateApis(userRole.name, req.url, req.method);
      
      }

      else{

        authApiCheck = await opaClient.authenticateApis('', req.url, req.method);

      }

      log.debug({ metaData: { class: 'builderCustomGfkMiddleware', method: 'requestRole' }, message: req.url });

      if(Object.prototype.hasOwnProperty.call(authApiCheck, 'result') && authApiCheck.result){
        
        next();

      }

      else {
      
        res.status(HttpStatusCode.FORBIDDEN).send(ProblemDetails.forbidden(req.originalUrl));
      
      }

    }
    
  });
};
