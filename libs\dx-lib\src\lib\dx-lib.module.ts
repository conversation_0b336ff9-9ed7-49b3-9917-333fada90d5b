import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgLibModule } from '@gfk/ng-lib';
import {
	AuthenticationInterceptor,
	AuthenticationService,
	AuthFacade,
	AUTH_INTERCEPTORS,
	AuthenticationTabCommunicationService,
	UserService,
	API_ROUTES,
	AUTH_DI_TOKENS,
} from './services/auth';
import { RedirectWebComponent } from './components/redirect-web/redirect-web.component';

const standaloneComponents = [
	RedirectWebComponent,
];
const services = [
	AuthenticationService,
	UserService,
	AuthenticationInterceptor,
	AuthenticationTabCommunicationService,
	AuthFacade,
	...AUTH_INTERCEPTORS,
];
@NgModule({
	imports: [CommonModule, NgLibModule, ...standaloneComponents],
	declarations: [],
	exports: [...standaloneComponents]
})
export class DxLibModule {
	static forRoot({ API_ROUTES: ROUTES_DI_VALUE }: AUTH_DI_TOKENS): ModuleWithProviders<DxLibModule> {
		return {
			ngModule: DxLibModule,
			providers: [
				{ provide: API_ROUTES, useValue: { BFF: ROUTES_DI_VALUE.BFF } },
				...services,
			],
		};
	}
}
