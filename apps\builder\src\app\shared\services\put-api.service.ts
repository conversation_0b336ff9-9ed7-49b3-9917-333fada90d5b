import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from './config.service';
import { updateBaseProject } from '../interfaces/updateBaseProject';
import { updateQCProject } from '../interfaces/updateQCProject';

@Injectable({
  providedIn: 'root'
})
export class PutApiService {
	
	apiUrl: string;
	userRoleUrl: string;

	constructor(private http: HttpClient, private config: ConfigService) {
		this.apiUrl = `${this.config.getApiUrl()}`;
		this.userRoleUrl = `${this.config.getUserRoleApiUrl()}`;
	}

	updateBaseProject(
        id?: number,
		name?: string,
		productGroups?:[],
		predecessors?: [],
		dataTypeId?: number,
		purposeId?: number,
		isRelevantForReportingEnabled?: boolean,
		deleted?: boolean
	): Observable<updateBaseProject> {
		const body = {
			name,
			predecessors,
			productGroups,
			dataTypeId,
			purposeId,
			isRelevantForReportingEnabled,
			deleted
		};
		return this.http.put<updateBaseProject>(this.apiUrl+'/BaseProjects/'+id, body);
  	}

	updateQCProject(
        id?: number,
		sqcMode?: number,
		isAutomatedPriceCheck?: boolean,
		isAutoLoad?: boolean,
		resetCorrectionTypeId?: number
	): Observable<updateQCProject> {
		const body = {
			sqcMode,
			isAutomatedPriceCheck,
			isAutoLoad,
			resetCorrectionTypeId
		};
		return this.http.put<updateQCProject>(this.apiUrl+'/qcprojects/'+id, body);
  	}

	updateQCPeriod(payload: any, qcPeriodID: number): Observable<any> {
		return this.http.put(this.apiUrl + '/qcperiods/qcperiods/'+qcPeriodID, payload);
	}

	updateUserRoles(payload: any): Observable<any> {
		return this.http.put(this.userRoleUrl + '/userroles', payload);
	}

	updateRetailerSeparation(
		requestId?: any,
		fromPeriodId?: any,
		toPeriodId?: any,
		resetCorrection?: boolean,
		extrapolation?: boolean,
		retailerSeperations?: any
	): Observable<any> {
		const body = {
			fromPeriodId,
			toPeriodId,
			resetCorrection,
			extrapolation,
			retailerSeperations
		};
		return this.http.put(this.apiUrl + '/retailerseperation/' + requestId, body);
	}

	updateRetailerSeparationStatus(
		retailerSeperationRequestIds?: any,
		statusId?: any,
		reason?: any
	): Observable<any> {
		const body = {
			retailerSeperationRequestIds,
			statusId,
			reason
		};
		return this.http.put(this.apiUrl + '/retailerseperation/status', body);
	}
}