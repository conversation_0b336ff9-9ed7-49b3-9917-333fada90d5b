#!/bin/bash
set -e
cp /usr/share/nginx/html/index.html /tmp
sed -i 's #{apiBaseUrl} '$API_URL' g' /tmp/index.html
sed -i 's #{apiVersion} '$API_VERSION' g' /tmp/index.html
sed -i 's #{jeeApiBaseUrl} '$JEE_API_URL' g' /tmp/index.html
sed -i 's #{jeeApiVersion} '$JEE_API_VERSION' g' /tmp/index.html
sed -i 's #{ENV} '$ENV' g' /tmp/index.html
sed -i 's #{ELASTIC_RUM_URL} '$ELASTIC_RUM_URL' g' /tmp/index.html
sed -i 's #{isElasticRUMEnabled} '$ELASTIC_RUM_ENABLED' g' /tmp/index.html
sed -i 's #{ELASTIC_RUM_SERVICE_NAME} '$ELASTIC_RUM_SERVICE_NAME' g' /tmp/index.html
sed -i 's #{UNDER_MAINTAINANCE} '$UNDER_MAINTAINANCE' g' /tmp/index.html
cp /tmp/index.html usr/share/nginx/html/index.html
