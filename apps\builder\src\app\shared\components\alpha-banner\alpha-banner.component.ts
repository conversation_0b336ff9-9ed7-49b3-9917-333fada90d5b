import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { BannerService } from '@builder/shared/services/banner.service';

@Component({
	selector: 'dx-alpha-banner',
	templateUrl: './alpha-banner.component.html',
	styleUrls: ['./alpha-banner.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AlphaBannerComponent {

	private readonly bannerService = inject(BannerService);
	feedbackLink = "https://forms.office.com/r/2hrjwEt9CQ";

	onCloseClick() {
		this.bannerService.hideBanner();
	}


	onInfoClick(event: MouseEvent) {
		event.preventDefault();
		event.stopPropagation();
	}
	

}
