import { Component, Input, OnInit } from '@angular/core';

interface ICard {
	title: string;
	description: string;
    url: string;
	tooltip:string;
	disableEnableButton:boolean;
}

@Component({
	selector: 'dx-card',
	templateUrl: './card.component.html',
	styleUrls: ['./card.component.scss'],
})
export class CardComponent implements OnInit {
	@Input() card!: ICard;
	disableEnableButton:string | undefined;
	disableColor:string | undefined;
	target_blank:string| undefined;

	ngOnInit(): void {
		this.disableEnableButton = this.card?.disableEnableButton ? 'javascript:void(0)': this.card?.url;
		this.disableColor = this.card?.disableEnableButton ? "disabled" :"enabled";
		this.target_blank  = this.card?.disableEnableButton ? '' : '_blank'
  }
}

