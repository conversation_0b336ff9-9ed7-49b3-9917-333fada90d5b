import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthFailedComponent } from '@builder/pages/auth-failed/auth-failed.component';
import { LoggedOutComponent } from '@builder/pages/logged-out/logged-out.component';
import { RedirectWebComponent } from '@dwh/dx-lib/src/lib/components/redirect-web/redirect-web.component';
import { AuthRoute } from '@dwh/dx-lib/src/lib/constants';
import { HomePageComponent } from './pages/home-page/home-page.component';
import { AuthGuard } from './shared/guards/auth.guard';

const routes: Routes = [
  { path: AuthRoute.REDIRECT, component: RedirectWebComponent },
  {
    path: 'auth-failed',
    component: AuthFailedComponent,
  },
  {
    path: 'logged-out',
    component: LoggedOutComponent,
  },
  {
    path: '',
    canActivate: [AuthGuard],
    component: HomePageComponent,
    loadChildren: () => import('@builder/pages/home-page/home-page.module').then((m) => m.HomePageModule),
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {})],
  exports: [RouterModule],
})
export class AppRoutingModule {
}
