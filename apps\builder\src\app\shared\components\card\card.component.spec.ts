import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CardComponent } from './card.component';
import { By } from '@angular/platform-browser';

describe('CardComponent', () => {
  let component: CardComponent;
  let fixture: ComponentFixture<CardComponent>;

  const mockCard = {
    title: 'Test Title',
    description: 'Test Description',
    url: 'http://example.com',
    tooltip: 'Test Tooltip',
    disableEnableButton: false,
  };

  const mockCardDisabled = {
    title: 'Test Title Disabled',
    description: 'Test Description Disabled',
    url: 'http://example.com',
    tooltip: 'Test Tooltip Disabled',
    disableEnableButton: true,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CardComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CardComponent);
    component = fixture.componentInstance;
    component.card = mockCard;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize disableEnableButton and disableColor correctly when disableEnableButton is false', () => {
    expect(component.disableEnableButton).toBe(mockCard.url);
    expect(component.disableColor).toBe('enabled');
    expect(component.target_blank).toBe('_blank');
  });

  it('should initialize disableEnableButton and disableColor correctly when disableEnableButton is true', () => {
    fixture.detectChanges();
    expect(component.disableColor);
	expect(component.target_blank);
  });

  it('should render the card title and description', () => {
    const titleElement = fixture.debugElement.query(By.css('.card-title')).nativeElement;
    expect(titleElement.textContent).toContain(mockCard.title);
  });

  it('should apply the correct link and target based on disableEnableButton', () => {
    const linkElement = fixture.debugElement.query(By.css('.is_enabled')).nativeElement;
    expect(linkElement.getAttribute('href')).toBe(mockCard.url);
    expect(linkElement.getAttribute('target')).toBe('_blank');
  });

  it('should apply the correct link and target when disableEnableButton is true', () => {
    fixture.detectChanges();
    const linkElement = fixture.debugElement.query(By.css('.is_enabled')).nativeElement;
    expect(linkElement.getAttribute('target'))
  });


});

