import { Injectable, inject } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpErrorResponse } from '@angular/common/http';
import { catchError, Observable, throwError } from 'rxjs';
import { EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';

import { UserService } from './user.service';

export const RETURN_URL = 'returnUrl';

@Injectable()
export class AuthorizationInterceptor implements HttpInterceptor {
	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);

	intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
		const roleData = localStorage.getItem('roleData');
		const userData = localStorage.getItem('user');
	
		const assignedCountries = this.getAssignedCountries(roleData);
		const modifiedRequest = this.createModifiedRequest(request, userData, assignedCountries);
	
		return next.handle(modifiedRequest).pipe(
			catchError((error) => this.handleError(error))
		);
	}
	
	private getAssignedCountries(roleData: string | null): number[] {
		if (roleData && roleData !== 'undefined') {
			const role = JSON.parse(roleData);
			if (role.name !== 'Master') {
				return role.countries
					.filter((country: any) => country.status === 'Approved')
					.map((country: any) => country.countryId);
			}
		}
		return [];
	}
	
	private createModifiedRequest(request: HttpRequest<unknown>, userData: string | null, assignedCountries: number[]): HttpRequest<unknown> {
		if (userData && userData !== 'undefined') {
			try {
				const user = JSON.parse(userData);
				const loggedInUserCountryData = assignedCountries.toString();
				const userName = user?.userName ?? '';
				const userId = user?.userId != null ? user.userId.toString() : '';
				return request.clone({
					setHeaders: {
						'custom-countryId': loggedInUserCountryData,
						'userName': userName,
						'userId': userId
					}
				});
			} catch (err) {
				console.error('Invalid user data in localStorage:', userData);
				return request;
			}
		}
		return request;
	}
	
	private handleError(error: any): Observable<never> {
		if (error instanceof HttpErrorResponse && error.status === 403) {
			setTimeout(() => {
				if (!UserService.forbiddenError) {
					this.notifyWidget("You are not authorized for this request. Please contact administrator.", 'error');
				}
			}, 1000);
		}
		return throwError(() => error);
	}

	notifyWidget(title: string, notificationType: string, message?: string): void {
		if(notificationType == 'info'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if(notificationType == 'error'){
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else{
			this.notificationService.showNotification(title, {
				message: message || '',
				duration : 15000,
				variant: NotificationVariant.SUCCESS,
			});
		}
	}
}
