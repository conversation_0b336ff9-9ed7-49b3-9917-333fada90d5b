import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FilterButtonComponent } from './filter-button.component';
import { By } from '@angular/platform-browser';

describe('FilterButtonComponent', () => {
  let component: FilterButtonComponent;
  let fixture: ComponentFixture<FilterButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FilterButtonComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FilterButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have the default icon as "filter"', () => {
    expect(component.icon).toBe('filter');
  });

  it('should display the input icon', () => {
    component.icon = 'custom-icon';
    fixture.detectChanges();

    const iconElement = fixture.debugElement.query(By.css('.filter-icon')).nativeElement;
    expect(iconElement.textContent).toContain('Filter');
  });

  it('should display the showFilterCount', () => {
    fixture.detectChanges();
    const countElement = fixture.debugElement.nativeElement;
    expect(countElement.textContent);
  });

  it('should emit isShownFilterTray event on showFilterTray call', () => {
    jest.spyOn(component.isShownFilterTray, 'emit');
    component.showFilterTray();
    expect(component.isShownFilterTray.emit).toHaveBeenCalledWith(true);
  });

  it('should call showFilterTray on button click', () => {
    jest.spyOn(component, 'showFilterTray');
    const buttonElement = fixture.debugElement.query(By.css('.filtertray-button')).nativeElement;
    buttonElement.click();
    expect(component.showFilterTray).toHaveBeenCalled();
  });
});
