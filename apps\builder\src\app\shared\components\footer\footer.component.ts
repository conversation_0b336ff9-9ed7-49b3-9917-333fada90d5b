import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SwUpdate } from '@angular/service-worker';

@Component({
  selector: 'dx-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
})
export class FooterComponent implements OnInit {
  version = '';
  private isFetchingVersion = false;
  currentYear = new Date().getFullYear();
  feedbackLink = "https://forms.office.com/r/2hrjwEt9CQ";  
  constructor(private http: HttpClient, private swUpdate: SwUpdate) {}

  ngOnInit(): void {
    this.checkVersion();

    if (this.swUpdate.isEnabled) {
      this.swUpdate.available.subscribe(() => {
        this.clearCacheAndReload();
      });
    }
  }


  checkVersion(): void {
    const noCacheUrl = `assets/version.txt?timestamp=${new Date().getTime()}`;

    this.isFetchingVersion = true;

    this.http.get(noCacheUrl, { responseType: 'text' }).subscribe({
      next: (data) => {
        const newVersion = data.trim();
        if (newVersion !== this.version) {
          this.version = newVersion;
        }
        this.isFetchingVersion = false;
      },
      error: (err) => {
        console.error('Failed to fetch version.txt:', err);
        this.isFetchingVersion = false;
      },
    });
  }
	onFeedbackClick() {
		window.open(this.feedbackLink, '_blank');
	}

  navigateToFeedbackLink() {
		window.open(this.feedbackLink, '_blank');
	}

  clearCacheAndReload(): void {
    const location = window.location as Location;

    if ('caches' in window) {
      caches.keys()
        .then((keyList) => {
          return Promise.all(keyList.map((key) => caches.delete(key))); 
        })
        .then(() => {
          (window.location as any).reload(); 
        })
        .catch(() => {
          (window.location as any).reload(); 
        });
    } else {
      location.reload(); 
    }
  }

}
