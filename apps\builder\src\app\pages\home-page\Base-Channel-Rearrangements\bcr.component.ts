import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PageChange } from '../base-projects/base-projects-add/base-projects-add.component';

@Component({
  selector: 'dx-bcr-details',
  templateUrl: './bcr.component.html',
  styleUrls: ['./bcr.component.scss']
})
export class BCRComponent implements OnInit {

  bcrData: any;
  bcrDataNotFound = false;
  bcrDetailList: any;
	visibleBCRDetailList: any[] = [];
	pageSizeForBCRDetail: any;
	currentPageForBCRDetail = 1;
	bcrDetailsModal!: boolean;
	readonly defaultPageSizeforBCRDetail = 10;
	readonly pageSizeOptionsForBCRDetail: number[] = [10, 25, 50, 100];
	bcrConflictsCount!: any;
  pageSizeOptions: number[] = [10, 25, 50, 100];

  constructor(
    private route: ActivatedRoute,
    private getApiService: GetApiService,
    private cdRef: ChangeDetectorRef
  ) {
    this.pageSizeForBCRDetail = this.defaultPageSizeforBCRDetail;
  }

  ngOnInit(): void {
    this.currentPageForBCRDetail = 1;
    this.pageSizeForBCRDetail = this.defaultPageSizeforBCRDetail;
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.fetchBCRDetails(id);
    }
  }


  fetchBCRDetails(id: string): void {
    this.getApiService.getInfoDetailsById(id).subscribe({
      next: (response) => {
        try {
          this.bcrData = JSON.parse(response.data);
          if (!this.bcrData || this.bcrData.length === 0) {
            this.bcrDataNotFound = true;
          } else {
            this.bcrDataNotFound = false;
            this.bcrDetailList = this.bcrData;
            this.visibleBCRDetailList = this.getPageBCRDetail(this.currentPageForBCRDetail, this.pageSizeForBCRDetail);
          }
        } catch (error) {
          this.bcrDataNotFound = true;
        }
        this.cdRef.detectChanges();
      },
      error: (error) => {
        this.bcrDataNotFound = true;
      },
    });
  }




	onPageChangeBCRDetail(event: PageChange) {
		this.currentPageForBCRDetail = event.pageIndex;
		this.pageSizeForBCRDetail = event.pageSize;
		this.visibleBCRDetailList = this.getPageBCRDetail(this.currentPageForBCRDetail, this.pageSizeForBCRDetail);
		this.cdRef.detectChanges();
	}

	private calculatePageStartForBCRDetail(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEndForBCRDetail(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

  private getPageBCRDetail(page: number, size: number) {
    const start = this.calculatePageStartForBCRDetail(page, size);
    const end = Math.min(this.bcrDetailList.length, this.calculatePageEndForBCRDetail(page, size));
    return this.bcrDetailList.slice(start, end);
  }
  trackByIndex(index: number): number {
    return index;
  }
}

