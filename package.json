{"name": "builder", "version": "0.0.0", "scripts": {"start": "nx run-many --target=serve --projects=builder-bff,builder", "start:bff": "nx serve builder-bff", "build": "nx build", "build:lib": "nx build dx-lib", "build:bff": "nx build builder-bff", "test": "nx run-many -t test --all --configuration ./jest.config.ts --code-coverage", "test:ci": "nx run-many -t test --all --configuration ./jest.config.ts -i --clearMocks --ci --code-coverage --runInBand", "test:pact": "nx pact builder", "test:pact:ci": "nx pact builder --ci", "test:watch": "nx test --watchAll", "code:coverage": "nx test --config ./jest.config.js --code-coverage --skip-nx-cache", "lint": "nx lint", "lint:ci": "nx run-many --target=lint --all", "test:lint": "nx test && nx lint", "e2e": "nx e2e", "extend": "nx generate component --project=dx-lib", "release": "yarn --cwd=lib/dx-lib install && yarn build:lib && yarn publish dist/libs/dx-lib --no-git-tag-version", "create:lib": "nx generate @nx/angular:library --buildable --publishable ", "create:component": "nx g @nx/angular:component", "create:service": "nx generate @nx/angular:service", "create:directive": "nx generate @nx/angular:directive", "create:pipe": "nx generate @nx/angular:pipe", "update": "nx migrate latest", "bundle:sourcemap": "ng build --source-map && source-map-explorer dist/apps/builder/main*", "bundle:analyze": "ng build --configuration development --stats-json && webpack-bundle-analyzer dist/apps/builder/stats.json", "svg:sprite": "svg2sprite apps/builder/src/assets/images/icons apps/builder/src/assets/images/sprites/sprite.svg", "ci:build": "npx nx run-many --target=build --projects=builder,dx-lib --parallel=true", "ci:test": "npx nx run-many --target=test --projects=builder,dx-lib --parallel=true --configuration ./jest.config.js --code-coverage", "ci:lint": "npx nx run-many --target=lint --projects=builder,dx-lib --parallel=true", "generate-version": "node generate-version.js"}, "private": true, "dependencies": {"@angular/animations": "18.2.5", "@angular/cdk": "18.2.7", "@angular/common": "18.2.5", "@angular/compiler": "18.2.5", "@angular/core": "18.2.5", "@angular/forms": "18.2.5", "@angular/material": "14.2.7", "@angular/material-moment-adapter": "14.2.7", "@angular/platform-browser": "18.2.5", "@angular/platform-browser-dynamic": "18.2.5", "@angular/pwa": "15", "@angular/router": "18.2.5", "@angular/service-worker": "15", "@azure/msal-node": "^3.6.4", "@gfk/bff": "0.6.2", "@gfk/ng-lib": "17.12.2", "@gfk/stencil-core": "0.10.2", "@gfk/style": "6.0.10", "@microsoft/signalr": "^8.0.7", "@ngneat/elf": "^2.0.0", "@ngrx/signals": "^18.0.2", "@opentelemetry/exporter-trace-otlp-http": "0.45.1", "@opentelemetry/instrumentation": "^0.203.0", "@opentelemetry/instrumentation-express": "^0.52.0", "@opentelemetry/instrumentation-http": "^0.203.0", "@opentelemetry/resources": "1.18.1", "@opentelemetry/sdk-trace-base": "1.18.0", "@opentelemetry/sdk-trace-node": "1.18.1", "@opentelemetry/semantic-conventions": "1.18.1", "angular-in-memory-web-api": "^0.13.0", "bootstrap": "^5.3.2", "classnames": "^2.5.1", "cookie-session": "^2.1.1", "express": "4.21.0", "express-http-proxy": "^2.1.1", "express-validator": "^7.2.1", "inversify": "^7.6.1", "lodash": "^4.17.21", "memory-cache": "^0.2.0", "moment": "^2.29.1", "moment-timezone": "^0.5.43", "ngx-ui-loader": "^13.0.0", "prom-client": "^15.1.3", "reflect-metadata": "0.2.2", "rxjs": "~7.8.1", "tslib": "^2.7.0", "winston": "^3.17.0", "zone.js": "0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "18.2.7", "@angular-devkit/core": "18.2.7", "@angular-devkit/schematics": "18.2.7", "@angular-eslint/eslint-plugin": "18.3.1", "@angular-eslint/eslint-plugin-template": "18.3.1", "@angular-eslint/template-parser": "18.3.1", "@angular/cli": "18.2.7", "@angular/compiler-cli": "18.2.5", "@angular/language-service": "18.2.5", "@jscutlery/swc-angular": "0.14.14", "@jscutlery/swc-angular-plugin": "0.14.14", "@nx/angular": "19.8.0", "@nx/esbuild": "19.8.0", "@nx/eslint": "19.8.0", "@nx/eslint-plugin": "19.8.0", "@nx/express": "19.8.0", "@nx/jest": "19.8.0", "@nx/js": "19.8.0", "@nx/node": "19.8.0", "@nx/webpack": "19.8.0", "@nx/workspace": "19.8.0", "@schematics/angular": "18.2.7", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.2.3", "@semantic-release/release-notes-generator": "^14.0.2", "@svgr/webpack": "8.1.0", "@swc-node/register": "1.10.9", "@swc/core": "1.5.29", "@swc/helpers": "0.5.13", "@swc/jest": "0.2.36", "@swc/types": "0.1.12", "@types/express": "^4.17.21", "@types/inversify": "^2.0.33", "@types/jest": "29.5.13", "@types/lodash": "^4.14.171", "@types/node": "20.16.6", "@typescript-eslint/eslint-plugin": "8.7.0", "@typescript-eslint/parser": "8.7.0", "@typescript-eslint/utils": "8.7.0", "autoprefixer": "^10.4.0", "conventional-changelog-conventionalcommits": "^8.0.0", "esbuild": "0.24.0", "eslint": "8.57.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "^29.7.0", "jest-junit": "^16.0.0", "jest-preset-angular": "14.1.1", "jsonc-eslint-parser": "^2.1.0", "ng-mocks": "^14.13.1", "ng-packagr": "18.2.1", "nx": "19.8.0", "postcss": "8.4.47", "postcss-preset-env": "10.0.5", "postcss-url": "^10.1.3", "prettier": "3.3.3", "prettier-eslint": "^16.0.0", "purgecss-whitelist-htmltags": "^1.0.7", "purgecss-whitelister": "^2.4.0", "semantic-release": "^24.2.0", "source-map-explorer": "^2.5.2", "swc-loader": "0.2.6", "ts-jest": "29.2.5", "ts-node": "10.9.2", "typescript": "5.5.4", "url-loader": "^4.1.1", "webpack": "5.93.0", "webpack-bundle-analyzer": "^4.10.1"}, "husky": {"hooks": {"pre-commit": "yarn lint:fix"}}}