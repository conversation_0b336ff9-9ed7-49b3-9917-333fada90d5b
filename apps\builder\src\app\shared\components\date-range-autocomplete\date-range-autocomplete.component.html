<div [ngClass]="isListing===true ? '' : 'flex'">
	<div [ngClass]="isListing===true ? '' : 'mr-3'" *ngIf="isListing!==true">
		<div class="mb-2.5">
			<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
				<span *ngIf="isListing!==true">Date Interval</span>
				<span *ngIf="isListing===true">History</span>
			</label>
		</div>
		<eds-select
			[value]="selectedHistory"
			eds-id="dateInterval-input"
			[options]="dateIntervalList ? dateIntervalList : []"
			[required]="false"
			[multiple]="false"
			class="date width-100"
			(edsChange)="changeHistory($event.detail)"
			[placeholder]="'Choose from list'">
		</eds-select>
	</div>
	<div *ngIf="isListing!==true">
		<div class="mb-2">
			<label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
				Date Range
			</label>
		</div>
		<mat-form-field appearance="outline" class="date width-100">
			<mat-date-range-input [formGroup]="dateRange" [rangePicker]="dateRangePicker" [disabled]="IsDateRangeDisabled">
				<input
					matStartDate
					placeholder="Start date"
					formControlName="start"
					#dateRangeStart
					(dateChange)="dateChange()"
					class="js-filter-inp-start-date"/>
				<input
					matEndDate
					placeholder="End date"
					formControlName="end"
					#dateRangeEnd
					id="endDate-input"
					(dateChange)="dateChange()"
					class="js-filter-inp-end-date"/>
			</mat-date-range-input>
			<mat-datepicker-toggle matSuffix [for]="dateRangePicker"></mat-datepicker-toggle>
			<mat-date-range-picker #dateRangePicker></mat-date-range-picker>
		</mat-form-field>
	</div>
</div>
