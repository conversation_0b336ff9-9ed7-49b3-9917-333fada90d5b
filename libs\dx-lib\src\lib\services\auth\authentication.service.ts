import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter, map, switchMap, Observable, tap, catchError, of, BehaviorSubject } from 'rxjs';
import { AuthRoute } from '../../constants/auth-routes';
import { AuthenticationTabCommunicationService } from './authentication-tab-communication.service';
import { API_ROUTES, BaseApi } from './tokens';

export interface AuthenticationSuccess {
	message: string;
	links: {
		[key: string]: string;
	};
}

function isAuthenticationSuccess(object: any): object is AuthenticationSuccess {
	try {
		return 'message' in object && 'links' in object;
	} catch (e) {
		return false;
	}
}

export interface SignOutResponse {
	links: { [key: string]: string };
}

@Injectable()
export class AuthenticationService {
	private isLoginInProcess$ = new BehaviorSubject(false);

	private readonly LOGIN_TAB_NAME = 'LMX_LOGIN_TAB';
	private windowHandle?: Window | null;

	get loginInProcess(): boolean {
		return this.isLoginInProcess$.getValue();
	}
	get loginInProcess$(): Observable<boolean> {
		return this.isLoginInProcess$.asObservable();
	}

	private startLoginProcess(): void {
		this.isLoginInProcess$.next(true);
	}

	private finishLoginProcess(): void {
		this.isLoginInProcess$.next(false);
	}

	constructor(
		private http: HttpClient,
		private router: Router,
		private authTabComService: AuthenticationTabCommunicationService,
		@Inject(API_ROUTES) private API: BaseApi
	) {}

	registerAuthentication() {
		this.observeRedirectionRoute();
		this.authTabComService.onAuthMessage.subscribe(() => {
			this.isLoginInProcess$.next(false);

			if (this.windowHandle) {
				this.windowHandle.close();
			} else {
				window.close();
			}
		});
	}

	private observeRedirectionRoute() {
		this.router.events
    .pipe(
        filter((event) => event instanceof NavigationEnd),
        map((event) => event as NavigationEnd),
        filter((event: NavigationEnd) => event.url.includes(AuthRoute.REDIRECT)),
        map((event) => event.url.split(AuthRoute.REDIRECT)[1]),
     	filter((queryParams) => queryParams?.length > 0),
        switchMap((queryParams) => this.requestCookieFromBFF(queryParams))
    )
    .subscribe({
        next: (authenticationSuccess) => this.authTabComService.publish(authenticationSuccess),
        complete: () => this.finishLoginProcess(),
    });
	}

	authenticate(): Observable<boolean> {
		return this.retrieveClientInfoViaRedirect();
	}

	signOut(): Observable<SignOutResponse> {
		return this.http.get<SignOutResponse>(`${this.API.BFF}/${AuthRoute.SIGNOUT}`);
	}

	private retrieveClientInfoViaRedirect(): Observable<boolean> {
		if (this.loginInProcess || window.name === this.LOGIN_TAB_NAME) {
			return this.authTabComService.onAuthMessage;
		}

		this.startLoginProcess();
		return this.getMSALSigninLink().pipe(
			tap(this.handleOpenPopup),
			tap(() => console.log('asdasdasd')),
			switchMap(() => this.authTabComService.onAuthMessage)
		);
	}

	private getMSALSigninLink(): Observable<string> {
		return this.http.get<{ signinUrl: string }>(`${this.API.BFF}/${AuthRoute.SIGNIN}`).pipe(map((result) => result.signinUrl));
	}

	private requestCookieFromBFF(queryParams: string): Observable<boolean> {
		return this.http.get<boolean>(`${this.API.BFF}/${AuthRoute.REDIRECT}${queryParams}`).pipe(
			catchError(() => of(false)),
			map((result) => isAuthenticationSuccess(result))
		);
	}

	private handleOpenPopup = (link: string): void => {
		this.windowHandle = this.popupWindow(link, this.LOGIN_TAB_NAME, window);
		if(!this.windowHandle) {
			alert('Popup blocked?!');
		} 
	};

	private popupWindow(url: string, windowName: string, win: Window = window) {
		return win.open(url, windowName, `left=280,top=280,width=480,height=320`);
	}
}
