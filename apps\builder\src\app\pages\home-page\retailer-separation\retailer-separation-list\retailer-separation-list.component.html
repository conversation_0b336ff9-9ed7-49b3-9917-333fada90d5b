<div class="relative-position rs-listing">
	<div class="heading p-1.5">
		<div class="header-spacing mx-6">
            <div>
				<h4 class="mb-0 font-bold">Retailer Separation</h4>
			</div>
			<div class="text-right">
				<button
					*ngIf="userRole"
					gfk-button
					type="primary"
					class="btn-secondary gfk-btn"
					routerLink="/retailer-separation/create"
					eds-id="add-retailer-separation-button"
					testId="add-retailer-separation-button">
					New Retailer Separation Request
				</button>
			</div>
		</div>
	</div>
    <div class="mx-6 mt-2">
        <form class="flex items-end" [formGroup]="filterForm" (ngSubmit)="confirmApply()">
            <div class="mr-3 flex-1	relative">
                <div class="mb-2">
                    <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                        Jira Ticket ID
                    </label>
                </div>
                <input
                    formControlName="jiraIds"
                    gfk-input
                    type="text" 
                    name="jiraTicketID"
                    [placeholder]="'Type here to search..'"
                    eds-id="jiraTicketID"
                    [required]="false"
                    inputmode="numeric" />
					<gfk-icon *ngIf="filterForm.get('jiraIds')?.value" [icon]="'close'" class="input-close-icon" (click)="closeClick()"></gfk-icon>
            </div>
            <div class="mr-3 flex-1	">
                <div class="mb-2">
                    <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                        Status
                    </label>
                </div>
                <eds-select
                    formControlName="requestStatusIds"
                    eds-id="status"
                    [options]="statusList"
                    [required]="false"
                    [multiple]="true"
                    [placeholder]="'Choose from list'">
                </eds-select>
            </div>
            <div class="mr-3 flex-1	">
                <div class="mb-2">
                    <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                        User
                    </label>
                </div>
                <eds-select
                    formControlName="usernames"
                    eds-id="user"
                    [options]="userList ? userList : []"
                    [required]="false"
                    [multiple]="true"
                    [placeholder]="'Choose from list'">
                </eds-select>
            </div>
            <div class="mr-3">
                <dx-date-range-autocomplete
                    class="filters-align"
                    [selected]="'1'"
                    (selectedDate)="selectedDateFilter($event)">
                </dx-date-range-autocomplete>
            </div>
            <div>
                <button
                    gfk-button
                    type="primary"
                    class="btn-secondary gfk-btn"
                    eds-id="apply-filter"
                    testId="apply-filter"
                    [disabled]="filterForm.invalid || loadTableData">
                    Apply
                </button>
            </div>
        </form>
    </div>
	<div class="mx-6">
		<article class="table-grid mt-5">
			<eds-table eds-id="base-project-table" hasRowSelect="checkbox" [isLoading]="loadTableData" (edsRowSelected)="onSelect($event, retailerSeparationData); rowsSelectedHandler($event, retailerSeparationData)">
				<thead slot="thead">
					<eds-tr variant="header">
						<eds-th variant="selector" [isSelected]="areAllSelectedWithPagination(visibleRetailerSeparationList)" [columnIndex]="0" *ngIf="userRole?.name === 'Master'">
							Jira Ticket ID
						</eds-th>
						<eds-th variant="" [isSelected]="areAllSelectedWithPagination(visibleRetailerSeparationList)" [columnIndex]="0" *ngIf="userRole?.name !== 'Master'">
							<span class="pl-9">Jira Ticket ID</span>
						</eds-th>
						<eds-th column-index="1">Reported By</eds-th>
						<eds-th column-index="2">Reported When</eds-th>
						<eds-th column-index="3">Status</eds-th>
						<eds-th column-index="3">Remarks</eds-th>
					</eds-tr>
				</thead>

				<tbody slot="tbody">
					<ng-container *ngIf="visibleRetailerSeparationList?.length > 0; else noData">
						<eds-tr *ngFor="let retailerSeparation of visibleRetailerSeparationList; trackBy: trackById" [rowIndex]="retailerSeparation.id">
							<eds-td [isSelected]="isSelected(retailerSeparation.id)" variant="selector" *ngIf="userRole?.name === 'Master'">
								<span class="rs-id-style cursor-pointer" [routerLink]="'/retailer-separation/list/'+retailerSeparation.id">{{ retailerSeparation.jiraId ? retailerSeparation.jiraId : 'Jira ID' }} &nbsp; &nbsp; | </span>
								<a class="rs-id-style cursor-pointer" [href]="(isProdBaseUrl ? 'https://adlm.nielseniq.com' : 'https://adlm-tst.nielseniq.com') + '/jira/browse/'+retailerSeparation.jiraId" target="_blank" rel="noopener">
									<svg 
										height="20" 
										preserveAspectRatio="xMidYMid" 
										viewBox="0 0 256 256" 
										width="30" 
										xmlns="http://www.w3.org/2000/svg" 
										xmlns:xlink="http://www.w3.org/1999/xlink">
										<linearGradient id="a">
											<stop offset=".18" stop-color="#0052cc"/><stop offset="1" stop-color="#2684ff"/>
										</linearGradient>
										<linearGradient id="b" x1="98.030868%" x2="58.887706%" xlink:href="#a" y1=".1606%" y2="40.765525%"/>
										<linearGradient id="c" x1="100.665247%" x2="55.40181%" xlink:href="#a" y1=".455032%" y2="44.726981%"/>
										<path d="m244.657778 0h-122.951111c0 14.7201046 5.847538 28.837312 16.256224 39.2459977 10.408686 10.4086858 24.525893 16.2562245 39.245998 16.2562245h22.648889v21.8666667c.019613 30.6252661 24.8414 55.4470541 55.466666 55.4666671v-122.1688893c0-5.89103736-4.775629-10.6666667-10.666666-10.6666667z" fill="#2684ff"/>
										<path d="m183.822222 61.2622222h-122.9511109c.0196127 30.6252666 24.8414001 55.4470538 55.4666669 55.4666668h22.648889v21.937778c.039238 30.625256 24.87694 55.431136 55.502222 55.431111v-122.1688891c0-5.8910373-4.775629-10.6666667-10.666667-10.6666667z" fill="url(#b)"/>
										<path d="m122.951111 122.488889h-122.951111c0 30.653031 24.8491913 55.502222 55.5022222 55.502222h22.72v21.866667c.0195448 30.597542 24.7980628 55.407869 55.3955558 55.466666v-122.168888c0-5.891038-4.77563-10.666667-10.666667-10.666667z" fill="url(#c)"/>
									</svg>
								</a>
							</eds-td>
							<eds-td *ngIf="userRole?.name !== 'Master'">
								<span class="pl-9 rs-id-style cursor-pointer" [routerLink]="'/retailer-separation/list/'+retailerSeparation.id">{{ retailerSeparation.jiraId ? retailerSeparation.jiraId : 'Jira ID' }} &nbsp; | </span>
								<a class="rs-id-style cursor-pointer absolute" [href]="(isProdBaseUrl ? 'https://adlm.nielseniq.com' : 'https://adlm-tst.nielseniq.com') + '/jira/browse/'+retailerSeparation.jiraId" target="_blank" rel="noopener">
									<svg 
										height="20" 
										preserveAspectRatio="xMidYMid" 
										viewBox="0 0 256 256" 
										width="30" 
										xmlns="http://www.w3.org/2000/svg" 
										xmlns:xlink="http://www.w3.org/1999/xlink">
										<linearGradient id="a">
											<stop offset=".18" stop-color="#0052cc"/><stop offset="1" stop-color="#2684ff"/>
										</linearGradient>
										<linearGradient id="b" x1="98.030868%" x2="58.887706%" xlink:href="#a" y1=".1606%" y2="40.765525%"/>
										<linearGradient id="c" x1="100.665247%" x2="55.40181%" xlink:href="#a" y1=".455032%" y2="44.726981%"/>
										<path d="m244.657778 0h-122.951111c0 14.7201046 5.847538 28.837312 16.256224 39.2459977 10.408686 10.4086858 24.525893 16.2562245 39.245998 16.2562245h22.648889v21.8666667c.019613 30.6252661 24.8414 55.4470541 55.466666 55.4666671v-122.1688893c0-5.89103736-4.775629-10.6666667-10.666666-10.6666667z" fill="#2684ff"/>
										<path d="m183.822222 61.2622222h-122.9511109c.0196127 30.6252666 24.8414001 55.4470538 55.4666669 55.4666668h22.648889v21.937778c.039238 30.625256 24.87694 55.431136 55.502222 55.431111v-122.1688891c0-5.8910373-4.775629-10.6666667-10.666667-10.6666667z" fill="url(#b)"/>
										<path d="m122.951111 122.488889h-122.951111c0 30.653031 24.8491913 55.502222 55.5022222 55.502222h22.72v21.866667c.0195448 30.597542 24.7980628 55.407869 55.3955558 55.466666v-122.168888c0-5.891038-4.77563-10.666667-10.666667-10.666667z" fill="url(#c)"/>
									</svg>
								</a>
							</eds-td>
							<eds-td>{{ retailerSeparation.requestDetails.updatedBy }}</eds-td>
                            <eds-td>{{ retailerSeparation.requestDetails.updatedWhenCET }}</eds-td>
                            <eds-td><span class="chip" [ngClass]="{
								'pending-state': retailerSeparation?.requestStatus === 'Pending',
								'executing-state': retailerSeparation?.requestStatus === 'Executing',
								'finished-state': retailerSeparation?.requestStatus === 'Finished',
								'edited-state': retailerSeparation?.requestStatus === 'Edited',
								'csv-available-state': retailerSeparation?.requestStatus === 'CSV Available'
							}">{{ retailerSeparation.requestStatus.toUpperCase() }}</span></eds-td>
                            <eds-td><span class="error-text">{{ retailerSeparation.isError ? 'ERROR' : '-' }}</span></eds-td>
						</eds-tr>
					</ng-container>
					<ng-template #noData>
						<eds-tr><eds-td colspan="6">{{filterApplied ? 'No records match the applied filter(s)' : 'Search or filter to view Retailer Separation Requests.'}}</eds-td></eds-tr>
					</ng-template>
				</tbody>
			</eds-table>

			<gfk-pagination
				*ngIf="retailerSeparationData?.length > 0 && retailerSeparationData?.length > defaultPageSize"
				id="pagination-create-ld"
				[itemsPerPageOptions]="pageSizeOptions"
				[totalCount]="retailerSeparationData?.length"
				[position]="'right'"
				[showItemsPerPage]="true"
				[showFirstAndLast]="true"
				[defaultPageSize]="defaultPageSize"
				[currentPage]="currentPage"
				(onPage)="onPageChangeRetailerSeparation($event)">
			</gfk-pagination>
		</article>
	</div>
	<div *ngIf="hasSelection">
		<div class="h-32"></div>
		<div class="selected-retailer-separation-footer">
			<div>
				{{ selectedCount + ' Retailer Seperation Request Selected' }}
			</div>
			<div class="flex">
				<button
					*ngIf="userRole?.name === 'Master'"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					[ngClass]="disableExecuteBtn ? '' : 'transparent-background-theme-btn'"
					[disabled]="disableExecuteBtn"
					(click)="openDeclineConfirmationModal()"
					eds-id="decline-request"
					testId="decline-request">
					Decline
				</button>
                <button
					*ngIf="userRole?.name === 'Master'"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					(click)="openConfirmationModal()"
					[disabled]="disableExecuteBtn"
					eds-id="execute-request"
					testId="execute-request">
					Execute
				</button>
                <button
					*ngIf="userRole?.name === 'Master'"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					[disabled]="disableFinishBtn"
					(click)="openFinishConfirmationModal()"
					eds-id="finished-request"
					testId="finished-request">
					Mark Finished
				</button>
			</div>
		</div>
	</div>
</div>

<!-- The Modal -->
<gfk-modal
	[triggerModal]="confirmationModal"
	modalTitle="Warning!"
	cancelTitle="Cancel"
	confirmTitle="Execute"
	(onAction)="confirmationModalAction($event)">
	<p>
        Are you sure, you want to start EXECUTING retailer separation process for all of the selected 
		Jira tickets and all the requests they contain? This action cannot be undone.
	</p>
</gfk-modal>
<!-- The Modal -->

<!-- The Modal -->
<gfk-modal
	[triggerModal]="declineConfirmationModal"
	modalTitle="Warning!"
	cancelTitle="Cancel"
	confirmTitle="Decline All"
	[confirmDisabled]="!declineMessage"
	(onAction)="declineConfirmationModalAction($event)">
	<p>Are you sure, you want to DECLINE all selected Jira Tickets and all the requests they contain? This action cannot be undone.</p>
	<p>Please mention the reason which will be added as a comment in affected Jira tickets.</p>
	<textarea class="w-full textarea-style" placeholder="Type here..." rows="8" maxlength="500" [(ngModel)]="declineMessage"></textarea>
</gfk-modal>
<!-- The Modal -->

<!-- The Modal -->
<gfk-modal
	[triggerModal]="finishConfirmationModal"
	modalTitle="Warning!"
	cancelTitle="Cancel"
	confirmTitle="Finish All"
	(onAction)="finishConfirmationModalAction($event)">
	<p>
        Are you sure, you want to mark all selected Jira tickets to FINISH? This action cannot be undone.
	</p>
</gfk-modal>
<!-- The Modal -->