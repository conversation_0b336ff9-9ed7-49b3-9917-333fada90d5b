import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UserRoleListComponent } from './user-role-list.component';
import { FormBuilder } from '@angular/forms';
import { of, throwError, timeout } from 'rxjs';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { PutApiService } from '@builder/shared/services/put-api.service';
import { EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { BannerService } from '@builder/shared/services/banner.service';
import { FilterService } from '@dwh/dx-lib/src';

describe('UserRoleListComponent', () => {
  	let component: UserRoleListComponent;
  	let fixture: ComponentFixture<UserRoleListComponent>;
	let getApiServiceMock: any;
	let postApiServiceMock: any;
	let putApiServiceMock: any;
	let notificationServiceMock: any;
	let bannerServiceMock: jest.Mocked<BannerService>;
	let filterServiceMock: any;
	

  beforeEach(async () => {

	getApiServiceMock = {
		getLoggedInUserRole: jest.fn().mockReturnValue(of([{ name: 'User', status: 'Approved' }])),
		getAsyncUserRoles: jest.fn().mockReturnValue(of([])),
		getCountries: jest.fn().mockReturnValue(of([]))
	};

	postApiServiceMock = {
		getUserRoleList: jest.fn().mockReturnValue(of([])),
		requestUserRoles: jest.fn().mockReturnValue(of([]))
	};

	putApiServiceMock = {
		updateUserRoles: jest.fn().mockReturnValue(of([])),
	};

	notificationServiceMock = {
		showNotification: jest.fn()
	};

	bannerServiceMock = {
		isVisible$: of(true),
	} as unknown as jest.Mocked<BannerService>;

	filterServiceMock = {
		filtersSelected$: jest.fn().mockReturnValue(of([])),
	};
	
	

    await TestBed.configureTestingModule({
      declarations: [UserRoleListComponent],
      providers: [
        FormBuilder,
		{ provide: GetApiService, useValue: getApiServiceMock },
        { provide: PostApiService, useValue: postApiServiceMock },
        { provide: PutApiService, useValue: putApiServiceMock },
        { provide: EdsNotificationService, useValue: notificationServiceMock },
        { provide: BannerService, useValue: bannerServiceMock },
        { provide: FilterService, useValue: filterServiceMock }
      ]
    }).compileComponents();

	fixture = TestBed.createComponent(UserRoleListComponent);
    component = fixture.componentInstance;
	fixture.detectChanges();
  });

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize user role form on ngOnInit', () => {
		component.ngOnInit();
		expect(component.userRoleFormGroup).toBeDefined();
		expect(component.countryGroup).toBeDefined();
	});

	it('should handle successful API response in getLoggedInUserRole()', () => {
		const mockResponse = [{ status: 'Approved', name: 'Master' }];
		getApiServiceMock.getLoggedInUserRole.mockReturnValue(of(mockResponse));
		component.getLoggedInUserRole();
		//expect(component.loggedInUserRole).toEqual(mockResponse[0]);
		expect(component.showFilterTrayBtn).toBe(true);
	});

	it('should handle API error in getLoggedInUserRole()', () => {
		getApiServiceMock.getLoggedInUserRole.mockReturnValue(throwError(() => new Error('Error')));
		component.getLoggedInUserRole();
	});

	it('should handle user roles on successful getUserRoles()', () => {
		const mockRoles = [{ id: 1, name: 'Admin' }];
		getApiServiceMock.getAsyncUserRoles.mockReturnValue(of(mockRoles));
		component.getUserRoles();
		expect(component.userRolesList).toEqual([{ label: 'Admin', value: '1' }]);
		expect(component.userRoleOptions).toContainEqual({ title: 'Admin', inputClass: '', name: 'radioGroup', value: '1', hasError: false, checked: false, disabled: false });
	});

	it('should handle API error in getUserRoles()', () => {
		getApiServiceMock.getAsyncUserRoles.mockReturnValue(throwError(() => new Error('Error')));
		component.getUserRoles();
	});

	it('should handle countries list on successful getCountryList()', () => {
		jest.spyOn(component, 'getCountryList');
		component.getCountryList();
		expect(component.getCountryList);
	});

	it('should handle API error in getCountryList()', () => {
		getApiServiceMock.getCountries.mockReturnValue(throwError(() => new Error('Error')));
		component.getCountryList();
	});

	it('should call getUserRolesList', () => {
		jest.spyOn(component, 'getUserRolesList');
		component.getUserRolesList();
		expect(component.getUserRolesList);
	});

	it('should call requestUserRoles', () => {
		jest.spyOn(component, 'requestUserRoles');
		component.requestUserRoles();
		expect(component.requestUserRoles);
	});

	it('should call editSelectedUserRoles', () => {
		jest.spyOn(component, 'editSelectedUserRoles');
		component.editSelectedUserRoles();
		expect(component.editSelectedUserRoles);
	});

	it('should call acceptSelectedUserRoles', () => {
		jest.spyOn(component, 'acceptSelectedUserRoles');
		component.acceptSelectedUserRoles();
		expect(component.acceptSelectedUserRoles);
	});

	it('should call declineSelectedUserRoles', () => {
		jest.spyOn(component, 'declineSelectedUserRoles');
		component.declineSelectedUserRoles();
		expect(component.declineSelectedUserRoles);
	});

	it('should call revokeSelectedUserRoles', () => {
		jest.spyOn(component, 'revokeSelectedUserRoles');
		component.revokeSelectedUserRoles();
		expect(component.revokeSelectedUserRoles);
	});
	
	it('should notify on error with correct message', () => {
		const testMessage = 'Detailed error description'; // Add a specific message for clarity
		component.notifyWidget('Test error message', 'error', testMessage);
	
		expect(notificationServiceMock.showNotification).toHaveBeenCalledWith('Test error message', {
			message: testMessage,
			duration: 15000,
			variant: NotificationVariant.ERROR,
		});
	});
	
	it('should call showSuccessAlert on successful notify', () => {
		const testMessage = 'Detailed success description'; // Add a specific message for clarity
		component.notifyWidget('Test success message', 'success', testMessage);

		expect(notificationServiceMock.showNotification).toHaveBeenCalledWith('Test success message', {
			message: testMessage,
			duration: 15000,
			variant: NotificationVariant.SUCCESS,
		});
	});

	it('should show only available assigned countries for Account Manager in edit modal (excluding already approved)', () => {
		// Setup test data
		component.loggedInUserRole = {
			name: 'Account Manager',
			countries: [
				{ countryId: 1, status: 'Approved' },
				{ countryId: 2, status: 'Approved' },
				{ countryId: 3, status: 'Pending' }
			]
		};

		component.countriesList = [
			{ label: 'Austria', value: '1' },
			{ label: 'Germany', value: '2' },
			{ label: 'Switzerland', value: '3' },
			{ label: 'France', value: '4' }
		];

Object.defineProperty(component, 'selectedEntities', {
  get: () => [{
    countries: [
      { countryId: 1, status: 'Approved' },
      { countryId: 2, status: 'Pending' }
    ]
  }]
});


		component.selectedItem = component.selectedEntities[0];

		// Call the method
		component.openEditConfirmationModal();

		// Verify that filteredCountryList excludes already approved countries for the user
		// Should only show Germany (2) because:
		// - Austria (1) is already approved for the user, so excluded
		// - Germany (2) is pending for the user, so included
		// - Switzerland (3) is only pending for AM (not approved), so excluded
		expect(component.filteredCountryList).toHaveLength(1);
		expect(component.filteredCountryList).toEqual([
			{ label: 'Germany', value: '2' }
		]);

		// Verify that the modal is opened
		expect(component.editConfirmationModal).toBe(true);
	});

	it('should show standard country filtering for non-Account Manager roles in edit modal', () => {
		// Setup test data
		component.loggedInUserRole = {
			name: 'Master'
		};

		component.countriesList = [
			{ label: 'Austria', value: '1' },
			{ label: 'Germany', value: '2' },
			{ label: 'Switzerland', value: '3' }
		];

Object.defineProperty(component, 'selectedEntities', {
  get: () => [{
    countries: [
      { countryId: 1, status: 'Approved' },
      { countryId: 2, status: 'Pending' }
    ]
  }]
});


		component.selectedItem = component.selectedEntities[0];

		// Call the method
		component.openEditConfirmationModal();

		// Verify that filteredCountryList excludes approved countries and includes pending ones
		expect(component.filteredCountryList).toHaveLength(2); // Switzerland (not in request) and Germany (pending)
		expect(component.filteredCountryList.map(c => c.value)).toContain('2'); // Germany (pending)
		expect(component.filteredCountryList.map(c => c.value)).toContain('3'); // Switzerland (not in request)
		expect(component.filteredCountryList.map(c => c.value)).not.toContain('1'); // Austria (approved)
	});

  // Add more tests as needed for other methods and functionalities
});
