// Example usage of gfk theme variables:
@use 'node_modules/@angular/material' as mat;
@use 'sass:map' as map;
@use '../../../../styles/theme/gfk-light.palette' as gfk;
@use '../../../../styles/theme/material-gfk.typography' as type;
@use '../../../../styles/theme/material-gfk-light.palette' as mat-colors;
@use '../../../../styles/theme/gfk-light.palette' as colors;
@use '@gfk/style' as gfk-style;

$config: mat.get-color-config(mat-colors.$theme);
$background: map-get($config, background);
$foreground: map-get($config, foreground);

%custom-component-selector {
  .something-that-looks-like-h2 {
    //@include mat.typography-level($gfk-typography, 'title');
    //or
    font-size: mat.font-size(type.$typography, 'title');
    font-family: mat.font-family(type.$typography, 'title');
    font-weight: mat.font-weight(type.$typography, 'title');
    line-height: mat.line-height(type.$typography, 'title');
    color: mat.get-color-from-palette($foreground, text);
    background: mat.get-color-from-palette($background, 'background');
  }
}

.headline {
    border-right: 1px solid #dadde3;
    padding-right: 15px;
    padding-left: 15px;
    cursor: pointer;
}

.headline:hover {
    color: #e55a00;
}

.headline-active {
    color: #e55a00;
}
  
.version {
    color: #808080;
}
  
.logout-button {
    background: transparent;
    width: auto;
}
  
.login-name {
    border-right: 1px solid white;
    font-size: 14px;
    margin-right: 16px;
    padding-right: 16px;
    margin-left: 16px;
}
  
.nav-bg {
    background-color: #f5f6f7;
}
  
.gfk-top-header {  
    .logo {
        padding: 0 32px 0 24px;
    }
    .gfk-nav {
        border-bottom: 0px !important;
        display: inline-flex;
        width: 100%;
        position: sticky;
        cursor: pointer;
        top: 0;
        z-index: 1;
    }
    .gfk-nav {
        border-bottom: 0px !important;
        &.sub-nav {
            background-color: #fff;
            box-shadow: 0 0 1px #bbc0c9;
            z-index: 2;
            position: fixed;
            cursor: pointer;
            font-weight: 400;
        }
    }
}
  
.gfk-secondary-header {
    width: 100%;
    position: fixed;
    z-index: 998;
    cursor: pointer;
  
    .gfk-nav {
        &.sub-nav {
            background-color: gfk-style.$white;      ;
            box-shadow: 0 0 1px #bbc0c9;
            z-index: 2;
            position: fixed;
            top: 48px;
            .nav-tab {
                line-height: 2.5rem;
            }
        }
    }
}