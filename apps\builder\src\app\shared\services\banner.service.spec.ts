import { TestBed } from '@angular/core/testing';
import { BannerService } from './banner.service';
import { StorageService } from './storage.service';

describe('BannerService', () => {
	let bannerService: BannerService;
	let storageMock: jest.Mocked<StorageService>;

	beforeEach(async () => {
		const storageMockInstance = {
			get: jest.fn(),
			set: jest.fn(),
			remove: jest.fn(),
		};
		await TestBed.configureTestingModule({
			providers: [BannerService, { provide: StorageService, useValue: storageMockInstance }],
		}).compileComponents();

		storageMock = storageMockInstance as jest.Mocked<StorageService>;

		bannerService = TestBed.get(BannerService);
	});

	it('should create', () => {
		expect(bannerService).toBeTruthy();
	});

	describe('isVisible$', () => {
		it('should return the inverted value of hideBanner$', () => {
			const hideBannerSubject = { getValue: jest.fn() };
			hideBannerSubject.getValue.mockReturnValue(false);

			bannerService.isVisible$.subscribe((isVisible) => expect(isVisible).toBe(true));
		});
	});

	describe('showBanner', () => {
		it('should remove the DX_BANNER_KEY from storage and set hideBanner$ to false', () => {
			bannerService.showBanner();

			expect(storageMock.remove).toHaveBeenCalledWith('dx-alpha-banner--hidden');
			expect((bannerService.hideBanner$ as any).getValue()).toBe(false);
		});
	});

	describe('hideBanner', () => {
		it('should set hideBanner$ to true and save the DX_BANNER_KEY in storage', () => {
			bannerService.hideBanner();

			expect((bannerService.hideBanner$ as any).getValue()).toBe(true);
			expect(storageMock.set).toHaveBeenCalledWith('dx-alpha-banner--hidden', true);
		});
	});
});
