import { Component } from '@angular/core';
import { LoaderService } from '@builder/shared/services/loader.service';

@Component({
    selector: 'dx-loader',
    templateUrl: './loader.component.html',
    styleUrls: ['./loader.component.scss']
})
export class LoaderComponent {
  
  loading = false;

  constructor(
    private loaderService: LoaderService, 
  ) {
      this.loaderService.isLoading.subscribe((v) => {
          this.loading = v;
      });
  }
}