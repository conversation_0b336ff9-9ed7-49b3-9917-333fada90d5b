// Example usage of gfk theme variables:
@use 'node_modules/@angular/material' as mat;
@use 'sass:map' as map;
@use '../../../../styles/theme/gfk-light.palette' as gfk;
@use '../../../../styles/theme/material-gfk.typography' as type;
@use '../../../../styles/theme/material-gfk-light.palette' as mat-colors;
@use '../../../../styles/theme/gfk-light.palette' as colors;
@use '@gfk/style' as gfk-style;

$config: mat.get-color-config(mat-colors.$theme);
$background: map-get($config, background);
$foreground: map-get($config, foreground);

%custom-component-selector {
  .something-that-looks-like-h2 {
    //@include mat.typography-level($gfk-typography, 'title');
    //or
    font-size: mat.font-size(type.$typography, 'title');
    font-family: mat.font-family(type.$typography, 'title');
    font-weight: mat.font-weight(type.$typography, 'title');
    line-height: mat.line-height(type.$typography, 'title');
    color: mat.get-color-from-palette($foreground, text);
    background: mat.get-color-from-palette($background, 'background');
  }
}

.gfk-footer {
  background-color: #ffffff;
  position: fixed;
  width: 100%;
  bottom: 0;
  padding-top: 15px;

  .gfk-footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;

    .content-bold-text {
      font-weight:  700;
    }

    .ml-4 {
      margin-left: 16px !important;
    }

    .version {
      font-weight: 700;
    }

    .feedback-link {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      text-decoration: none;
      cursor: pointer;
      transition: color 0.2s ease;
    
      font-weight: 700; 
      color: mat.get-color-from-palette($foreground, text); 
      font-size: 13px; 
    
      eds-icon {
        transition: color 0.2s ease;
      }
    
      &:hover {
        color: #f60; 
    
        eds-icon {
          color: #f60;
        }
      }
    }
    
    

    

  }
}
