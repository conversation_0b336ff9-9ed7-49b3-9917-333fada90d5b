{"name": "dx-lib", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "libs/dx-lib/src", "prefix": "dx", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/libs/dx-lib"], "options": {"project": "libs/dx-lib/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/dx-lib/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/dx-lib/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/dx-lib"], "options": {"jestConfig": "libs/dx-lib/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/eslint:lint"}}}