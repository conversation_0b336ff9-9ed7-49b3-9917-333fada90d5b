@use 'sass:map' as map;
// Typography: Typography config that overrides the font-family based on https://www.figma.com/file/fH6tO0HhgzrMPiWgnV2yUb/Design-library?node-id=413%3A0
$base-font-size: 16px;
$typography: (
  'font-size': $base-font-size,
  'line-height': 1.5,
  //24px at 16px, NOT REM, since it need to multiply elem font-size, not body font-size
  'font-family': 'Lato, sans-serif',
  'font-weight': 400,
  'bold-weight': 700,
  'letter-spacing': 0.015625rem,
  //0.25px at 16px
  'h1-font-size': 2rem,
  'h2-font-size': 1.75rem,
  'h3-font-size': 1.5rem,
  'h4-font-size': 1.25rem,
  'intro-font-size': 1.125rem,
  'text-font-size': 1rem,
  'caption-font-size': 0.875rem,
  'micro-font-size': 0.75rem,
  'giant-font-size': 6rem,
);

@mixin normalize() {
  blockquote,
  dl,
  dd,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  figure,
  p,
  pre {
    margin: 0;
  }

  button {
    background-color: transparent;
    background-image: none;
  }

  button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color;
  }

  fieldset {
    margin: 0;
    padding: 0;
  }

  ol,
  ul {
    margin: 0;
    padding: 0;
  }

  //body {
  //    font-family: inherit;
  //    line-height: inherit;
  //}

  *,
  ::before,
  ::after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: currentColor;
  }

  hr {
    border-top-width: 1px;
  }

  img {
    border-style: solid;
  }

  textarea {
    resize: vertical;
  }

  input::placeholder,
  textarea::placeholder {
    opacity: 1;
    color: #a1a1aa;
  }

  button,
  [role='button'] {
    cursor: pointer;
  }

  table {
    border-collapse: collapse;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    text-decoration: inherit;
  }

  button,
  input,
  optgroup,
  select,
  textarea {
    padding: 0;
    line-height: inherit;
    color: inherit;
  }

  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    display: block;
    vertical-align: middle;
  }

  img,
  video {
    max-width: 100%;
    height: auto;
  }
}

@mixin typography-base($typography) {
  @at-root html#{&} {
    font-family: #{map.get($typography, 'font-family')};
    font-size: map.get($typography, 'font-size');
    line-height: map.get($typography, 'line-height');
    font-weight: map.get($typography, 'font-weight');
    letter-spacing: map.get($typography, 'letter-spacing');
  }
}

@mixin typography-all($typography) {
  @include typography-base($typography);

  h1 {
    font-size: map.get($typography, 'h1-font-size');
  }

  h2 {
    font-size: map.get($typography, 'h2-font-size');
  }

  h3 {
    font-size: map.get($typography, 'h3-font-size');
  }

  h4 {
    font-size: map.get($typography, 'h4-font-size');
  }

  .giant {
    font-size: map.get($typography, 'giant-font-size');
  }
}
