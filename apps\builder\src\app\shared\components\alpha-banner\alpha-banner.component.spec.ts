import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AlphaBannerComponent } from './alpha-banner.component';
import { BannerService } from '@builder/shared/services/banner.service';
import { By } from '@angular/platform-browser';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('AlphaBannerComponent', () => {
  let component: AlphaBannerComponent;
  let fixture: ComponentFixture<AlphaBannerComponent>;
  let bannerService: jest.Mocked<BannerService>;

  beforeEach(async () => {
    bannerService = {
      hideBanner: jest.fn(),
    } as unknown as jest.Mocked<BannerService>;

    await TestBed.configureTestingModule({
      declarations: [AlphaBannerComponent],
      providers: [{ provide: BannerService, useValue: bannerService }],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AlphaBannerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });


  it('should prevent default and stop propagation on info click', () => {
    const event = new MouseEvent('click');
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
    const stopPropagationSpy = jest.spyOn(event, 'stopPropagation');
    component.onInfoClick(event);
    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(stopPropagationSpy).toHaveBeenCalled();
  });

});
