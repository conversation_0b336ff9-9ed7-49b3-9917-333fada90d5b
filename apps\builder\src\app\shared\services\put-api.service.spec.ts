import { TestBed } from '@angular/core/testing';

import { PutApiService } from './put-api.service';
import { HttpClient, HttpHandler } from '@angular/common/http';

describe('PutApiService', () => {
  let service: PutApiService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        HttpClient,
        HttpHandler
      ]
    });
    service = TestBed.inject(PutApiService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
