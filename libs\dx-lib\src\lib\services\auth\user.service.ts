import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, Subject, map, share, tap } from 'rxjs';
import { AuthenticationService, SignOutResponse } from './authentication.service';
import { API_ROUTES, BaseApi } from './tokens';
import { Router } from '@angular/router';
export interface User {
	userId: number;
	userName: string;
	firstName: string;
	lastName: string;
	email: string;
	roles: string[];
	allowedApps: {
		[appName: string]: 1;
	};
}

@Injectable()
export class UserService {
	private _user$: Subject<User | void> = new ReplaySubject();
	
	static forbiddenError: boolean;

	get user$(): Observable<User> {
		return this._user$.asObservable().pipe(map((user) => user as User));
	}

	private get url(): string {
		return `${this.API.BFF}/me`;
	}
	constructor(private http: HttpClient, private authService: AuthenticationService, @Inject(API_ROUTES) private readonly API: BaseApi,private router: Router) {}

	signInUser(): void {
		this.get().subscribe(
			(user) => {
			  this._user$.next(user);
			  if(user.userId && window.location.href.split('/')[3]==''){
				this.router.navigate(['home']) 
			  }
			}
		  );
	}

	signOut(): Observable<SignOutResponse> {
		return this.authService.signOut().pipe(tap(() => this.clear()));
	}

	get(): Observable<User> {
		return this.http.get<User>(this.url).pipe(share());
	}

	clear(): void {
		this._user$.next();
	}
}
