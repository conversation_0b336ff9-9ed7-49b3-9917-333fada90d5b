import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { SourceBPListComponent } from './source-bp-list.component';

describe('SourceBPListComponent', () => {
  
  const postApiServiceMock = {
    getBaseProjectsListForFilter: jest.fn().mockReturnValue(of({})),
    addRetailerSeparation: jest.fn().mockReturnValue(of([]))
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule],
      declarations: [SourceBPListComponent],
      providers: [
        FormBuilder,
        { provide: PostApiService, useValue: postApiServiceMock },
      ],
    }).compileComponents();
  });

  it('should create', () => {
    expect(SourceBPListComponent).toBeTruthy();
  });

  // Add more test cases here...
});

