import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RedirectWebComponent } from './redirect-web.component';

describe('RedirectWebComponent', () => {
	let component: RedirectWebComponent;
	let fixture: ComponentFixture<RedirectWebComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			declarations: [],
			imports: [CommonModule, RedirectWebComponent],
		}).compileComponents();

		fixture = TestBed.createComponent(RedirectWebComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
