{"name": "builder-bff", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/builder-bff/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/builder-bff", "main": "apps/builder-bff/src/main.ts", "tsConfig": "apps/builder-bff/tsconfig.app.json", "webpackConfig": "apps/builder-bff/webpack.config.js"}, "configurations": {"development": {}, "production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/builder-bff/src/environments/environment.ts", "with": "apps/builder-bff/src/environments/environment.prod.ts"}], "externalDependencies": "none"}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "builder-bff:build"}, "configurations": {"development": {"buildTarget": "builder-bff:build:development"}, "production": {"buildTarget": "builder-bff:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": []}