import { createStore, select, setProps, withProps } from '@ngneat/elf';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';

export interface Filter {
	name: string;
	values: any[];
	options: any[];
	slice: string;
}

export interface FilterRecords {
	[key: string]: Filter;
}

@Injectable({
	providedIn: 'root',
})
export class FilterService {
	filtersStore = createStore({ name: 'filters' }, withProps<FilterRecords>({}));
	filtersAll$ = this.filtersStore.pipe(select((state) => state));
	filtersSelected$ = this.filtersStore.pipe(
		select((filters) => {
			return Object.fromEntries(
				Object.entries(filters).filter(([key, filter]) => {
					if (filter.values) return filter.values.length && filter.slice == window.location.href.split('/')[3] ? [key, filter] : false;
					else return false;
				})
			);
		})
	);
	filterCount$ = this.filtersSelected$.pipe(map((selectedFilters: FilterRecords) => Number(Object.keys(selectedFilters).length)));

	setFilters = (filters: FilterRecords) => {
		this.filtersStore.update(
			setProps((state) => {
				return {
					...state,
					...filters,
				};
			})
		);
	};
	resetFilter = (filterName: string) => {
		this.filtersStore.update(
			setProps((state) => {
				const existingFilter = state[filterName];
				if (existingFilter) {
					const newState = { ...state };
					existingFilter.values = [];
					newState[filterName] = existingFilter;
					return newState;
				} else {
					return {
						...state,
					};
				}
			})
		);
	};
}
