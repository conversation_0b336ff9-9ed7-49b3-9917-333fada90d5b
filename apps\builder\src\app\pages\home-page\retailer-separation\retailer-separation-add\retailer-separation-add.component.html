<div>
    <div class="heading p-1.5" *ngIf="!requestID">
        <div class="mx-6">
            <eds-breadcrumbs>
                <a routerLink="/retailer-separation/list">Retailer Separation</a>
                <a routerLink="/retailer-separation/create">Create New Retailer Separation Request</a>
            </eds-breadcrumbs>
        </div>
        <div class="mx-6 flex items-center gap-2 justify-between">
            <h3 class="mb-0 font-bold">Create New Request</h3>
        </div>
    </div>
    <div class="heading p-1.5" *ngIf="requestID && rsRequestData && rsRequestData?.jiraId">
		<div class="header-spacing mx-6">
            <div>
				<eds-breadcrumbs>
					<a routerLink="/retailer-separation/list" [queryParams]="{}">Retailer Separation</a>
                    <a [routerLink]="'/retailer-separation/list/'+rsRequestData?.id" [queryParams]="{}">{{rsRequestData?.jiraId}}</a>
					<a [routerLink]="'/retailer-separation/list/'+rsRequestData?.id" [queryParams]="{}" *ngIf="selectedSourceBPID">Source BP # {{selectedSourceBPID}}</a>
				</eds-breadcrumbs>
				<div class="flex items-center">
					<h4 class="mb-0 font-bold">{{rsRequestData?.jiraId +' |'}}</h4>
					<a class="rs-id-style cursor-pointer text-lg" [href]="(isProdBaseUrl ? 'https://adlm.nielseniq.com' : 'https://adlm-tst.nielseniq.com') + '/jira/browse/'+rsRequestData?.jiraId" target="_blank" rel="noopener">
						<svg 
							class="mt-2"
							height="20" 
							preserveAspectRatio="xMidYMid" 
							viewBox="0 0 256 256" 
							width="30" 
							xmlns="http://www.w3.org/2000/svg" 
							xmlns:xlink="http://www.w3.org/1999/xlink">
							<linearGradient id="a">
								<stop offset=".18" stop-color="#0052cc"/><stop offset="1" stop-color="#2684ff"/>
							</linearGradient>
							<linearGradient id="b" x1="98.030868%" x2="58.887706%" xlink:href="#a" y1=".1606%" y2="40.765525%"/>
							<linearGradient id="c" x1="100.665247%" x2="55.40181%" xlink:href="#a" y1=".455032%" y2="44.726981%"/>
							<path d="m244.657778 0h-122.951111c0 14.7201046 5.847538 28.837312 16.256224 39.2459977 10.408686 10.4086858 24.525893 16.2562245 39.245998 16.2562245h22.648889v21.8666667c.019613 30.6252661 24.8414 55.4470541 55.466666 55.4666671v-122.1688893c0-5.89103736-4.775629-10.6666667-10.666666-10.6666667z" fill="#2684ff"/>
							<path d="m183.822222 61.2622222h-122.9511109c.0196127 30.6252666 24.8414001 55.4470538 55.4666669 55.4666668h22.648889v21.937778c.039238 30.625256 24.87694 55.431136 55.502222 55.431111v-122.1688891c0-5.8910373-4.775629-10.6666667-10.666667-10.6666667z" fill="url(#b)"/>
							<path d="m122.951111 122.488889h-122.951111c0 30.653031 24.8491913 55.502222 55.5022222 55.502222h22.72v21.866667c.0195448 30.597542 24.7980628 55.407869 55.3955558 55.466666v-122.168888c0-5.891038-4.77563-10.666667-10.666667-10.666667z" fill="url(#c)"/>
						</svg>
					</a>
					<div class="ml-4" *ngIf="rsRequestData?.requestStatus">
						<span class="chip" [ngClass]="
							{
								'pending-state': rsRequestData?.requestStatus === 'Pending',
								'executing-state': rsRequestData?.requestStatus === 'Executing',
								'finished-state': rsRequestData?.requestStatus === 'Finished',
								'edited-state': rsRequestData?.requestStatus === 'Edited',
                                'csv-available-state': rsRequestData?.requestStatus === 'CSV Available'
							}">
							{{ rsRequestData?.requestStatus?.toUpperCase() }}
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
    <div>
        <form [formGroup]="retailerSeparationForm" (ngSubmit)="saveRetailerSeparation()" class="mt-4 mx-20">
            <div class="w-2/5">
                <div>
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            {{requestID ? 'Selected ' : ''}}Source Base Project
                            <ng-container>*</ng-container>
                        </label>
                    </div>
                    <eds-select
                        *ngIf="!requestID"
                        formControlName="sourceBPId"
                        eds-id="sourceBPId"
                        [options]="sourceBPList ? sourceBPList : []"
                        [required]="true"
                        [multiple]="true"
                        [placeholder]="'Start typing'"
                        [value]="retailerSeparationForm.get('sourceBPId')?.value">
                    </eds-select>
                    <p class="error-text" *ngIf="showSourceBPError">You cannot select more than 20 Source Base Projects.</p>
                    <div *ngIf="requestID" class="selected-base-project">
                        <div class="gfk-chip-container">
                            <gfk-chip *ngFor="let sourceBP of selectedSourceBPLabels"><span>{{sourceBP}}</span></gfk-chip>
                        </div>
                    </div>
                </div>
                <div class="mt-5">
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            From Period
                            <ng-container>*</ng-container>
                        </label>
                    </div>
                    <div class="flex items-center">
                        <eds-checkbox
                            edsId="allFromPeriod"
                            [disabled]="retailerSeparationForm.get('sourceBPId')?.value?.length > 1 || disablePeriodCheckbox"
                            hasError="false"
                            indeterminate="false"
                            [checked]="fromPeriodCheckBoxState"
                            name="fromPeriodAll"
                            (edsChange)="disableInputField($event, 'fromPeriodId', 'allFromPeriod')">
                        </eds-checkbox>
                        <span class="ml-4">ALL</span>
                        <input
                            formControlName="fromPeriodId"
                            gfk-input
                            type="text" 
                            name="fromPeriodId"
                            [placeholder]="'Period ID'"
                            [value]="retailerSeparationForm.get('fromPeriodId')?.value"
                            eds-id="fromPeriodId"
                            class="ml-5 w-2/3"
                            maxlength="14"
                            minlength="14"
                            [required]="true"
                            pattern="[0-9]*"
                            inputmode="numeric" />
                    </div>
                </div>
                <div class="mt-5">
                    <div class="mb-2">
                        <label class="leading-relaxed font-bold text-md h-6 mb-2 items-center gap-1">
                            To Period
                            <ng-container>*</ng-container>
                        </label>
                    </div>
                    <div class="flex items-center">
                        <eds-checkbox
                            edsId="allToPeriod"
                            [disabled]="retailerSeparationForm.get('sourceBPId')?.value?.length > 1 || disablePeriodCheckbox"
                            hasError="false"
                            indeterminate="false"
                            [checked]="toPeriodCheckBoxState"
                            name="allToPeriod"
                            (edsChange)="disableInputField($event, 'toPeriodId', 'allToPeriod')">
                        </eds-checkbox>
                        <span class="ml-4">ALL</span>
                        <input
                            formControlName="toPeriodId"
                            gfk-input
                            type="text" 
                            name="toPeriodId"
                            [placeholder]="'Period ID'"
                            [value]="retailerSeparationForm.get('toPeriodId')?.value"
                            eds-id="toPeriodId"
                            class="ml-5 w-2/3"
                            maxlength="14"
                            minlength="14"
                            [required]="true"
                            pattern="[0-9]*"
                            inputmode="numeric" />
                    </div>
                </div>
                <div class="mt-5">
                    <gfk-radio-buttons
                        formControlName="extrapolation"
                        [options]="extrapolationOptions"
                        [labelGroup]="extrapolationFactorLabelGroup"
                        name="extrapolation"
                        [isInline]="true"
                        [disabled]="false"
                        eds-id="extrapolation">
                    </gfk-radio-buttons>
                </div>
                <div class="mt-5">
                    <gfk-radio-buttons
                        formControlName="resetCorrection"
                        [options]="resetCorrectionsOptions"
                        [labelGroup]="resetCorrectionsLabelGroup"
                        name="resetCorrection"
                        [isInline]="true"
                        [disabled]="false"
                        eds-id="resetCorrection">
                    </gfk-radio-buttons>
                </div>
            </div>
            <div class="flex justify-between w-2/5 mt-5">
                <button
                    *ngIf="!rsRequestData"
                    gfk-button
                    type="primary"
                    [disabled]="retailerSeparationForm?.invalid || showSourceBPError || loadTableData"
                    eds-id="create-base-project-button"
                    testId="create-base-project-button"
                    class="btn-secondary gfk-btn">
                    Save & Create Jira Ticket
                </button>
                <button
                    *ngIf="rsRequestData"
                    [routerLink]="'/retailer-separation/list/'+rsRequestData?.id"
                    [queryParams]="{}"
                    gfk-button
                    type="button"
                    class="jira-button-margin-right"
                    [ngClass]="loadTableData ? '' : 'transparent-background-theme-btn'"
                    [disabled]="loadTableData"
                    eds-id="back-button"
                    testId="back-button">
                    Back
                </button>
                <button
                    *ngIf="userRole?.name === 'Master' && rsRequestData?.requestStatus === 'Pending' && (editAllParam === 'true' || editParam === 'true')"
                    gfk-button
                    type="primary"
                    [disabled]="loadTableData"
                    eds-id="save-button"
                    testId="save-button">
                    Save
                </button>
                <button
                    *ngIf="userRole?.name === 'Master' && rsRequestData?.requestStatus === 'Pending' && editAllParam === 'false'"
                    (click)="openDeleteConfirmationModal()"
                    gfk-button
                    type="button"
                    [ngClass]="loadTableData ? '' : 'transparent-background-theme-btn'"
                    [disabled]="loadTableData"
                    eds-id="back-button"
                    testId="back-button">
                    Delete
                </button>
                <button
                    *ngIf="userRole?.name === 'Master' && rsRequestData?.requestStatus === 'CSV Available'"
                    (click)="openMarkErrorConfirmationModal()"
                    gfk-button
                    type="button"
                    [disabled]="loadTableData"
                    eds-id="back-button"
                    testId="back-button">
                    {{(rsRequestData?.isError) ? 'Remove Error' : 'Mark Error'}}
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Modal -->
<gfk-modal
	[triggerModal]="deleteConfirmationModal"
	modalTitle="Warning!"
	cancelTitle="Cancel"
	confirmTitle="Delete"
    [confirmDisabled]="!deleteMessage"
	(onAction)="deleteRetailerSeparationRequest($event)">
	<p>Are you sure, you want to mark DELETE to this request?</p>
    <p>Please mention the reason which will be added as a comment in Jira ticket.</p>
    <textarea class="w-full textarea-style" placeholder="Type here..." rows="8" maxlength="500" [(ngModel)]="deleteMessage"></textarea>
</gfk-modal>
<!-- Delete Modal -->

<!-- Mark Error Modal -->
<gfk-modal
	[triggerModal]="markErrorConfirmationModal"
	modalTitle="Warning!"
	cancelTitle="Cancel"
	[confirmTitle]="(rsRequestData?.isError) ? 'Remove Error' : 'Mark Error'"
    [confirmDisabled]="!errorMessage && !rsRequestData?.isError"
	(onAction)="markErrorConfirmationModalAction($event)">
	<p>Are you sure, you want to {{rsRequestData?.isError ? 'remove ERROR from' : 'mark ERROR to'}} this request?</p>
    <p *ngIf="!rsRequestData?.isError">Please mention the reason which will be added as a comment in Jira ticket.</p>
    <textarea class="w-full textarea-style" placeholder="Type here..." rows="8" maxlength="500" [(ngModel)]="errorMessage" *ngIf="!rsRequestData?.isError"></textarea>
</gfk-modal>
<!-- Mark Error Modal -->

<!-- QC Status BP Modal -->
<gfk-modal
    [triggerModal]="IRValidationModal"
    modalTitle="IR Separation Request Failed"
    cancelTitle=""
    confirmTitle="Close"
    (onAction)="closeIRValidationModal()">
    <div>
        <p>
            The following BP IDs have a QC Period with a status set to QC, which prevents I/R Separation request to be processed:
        </p>
        <p class="text-brand">
            BP ID: {{bpIds}}
        </p>
    </div>
</gfk-modal>
<!-- QC Status BP Modal -->