$font-family: '<PERSON><PERSON>';
$default-font-size: 16px;
$letter-spacing: 0.25px;
$text-align: left;
$font-unit-type: rem;
$font-style: normal;

@mixin typography($font-size, $font-weight, $line-height) {
  font-family: $font-family;
  font-size: $font-size + $font-unit-type;
  line-height: $line-height;
  font-weight: $font-weight;
  font-style: $font-style;
  letter-spacing: $letter-spacing;
  text-align: $text-align;
}

// Regular
.regular-giant {
  @include typography(3, 400, 72px);
}

.regular-h1 {
  @include typography(2, 400, 48px);
}

.regular-h1-tablet {
  @include typography(1.75, 400, 42px);
}

.regular-h2 {
  @include typography(1.75, 400, 42px);
}

.regular-h2-tablet {
  @include typography(1.5, 400, 36px);
}

.regular-h3 {
  @include typography(1.5, 400, 36px);
}

.regular-h3-tablet {
  @include typography(1.25, 400, 30px);
}

.regular-h4 {
  @include typography(1.25, 400, 30px);
}

.regular-intro {
  @include typography(1.125, 400, 27px);
}

.regular-body {
  @include typography(1.125, 400, 27px);
}

.regular-caption {
  @include typography(0.875, 400, 21px);
}

.regular-micro {
  @include typography(0.75, 400, 18px);
}

// Bold
.bold-giant {
  @include typography(3, 700, 72px);
}

.bold-h1 {
  @include typography(2, 700, 48px);
}

.bold-h1-tablet {
  @include typography(1.75, 700, 42px);
}

.bold-h2 {
  @include typography(1.75, 700, 42px);
}

.bold-h2-tablet {
  @include typography(1.5, 700, 36px);
}

.bold-h3 {
  @include typography(1.5, 700, 36px);
}

.bold-h3-tablet {
  @include typography(1.25, 700, 30px);
}

.bold-h4 {
  @include typography(1.25, 700, 30px);
}

.bold-intro {
  @include typography(1.125, 700, 27px);
}

.bold-body {
  @include typography(1.125, 700, 27px);
}

.bold-caption {
  @include typography(0.875, 700, 21px);
}

.bold-micro {
  @include typography(0.75, 700, 18px);
}

// Underline
.underline-giant {
  @include typography(3, 400, 72px);
  text-decoration: underline;
}

.underline-h1 {
  @include typography(2, 400, 48px);
  text-decoration: underline;
}

.underline-h1-tablet {
  @include typography(1.75, 400, 42px);
  text-decoration: underline;
}

.underline-h2 {
  @include typography(1.75, 400, 42px);
  text-decoration: underline;
}

.underline-h2-tablet {
  @include typography(1.5, 400, 36px);
  text-decoration: underline;
}

.underline-h3 {
  @include typography(1.5, 400, 36px);
  text-decoration: underline;
}

.underline-h3-tablet {
  @include typography(1.25, 400, 30px);
  text-decoration: underline;
}

.underline-h4 {
  @include typography(1.25, 400, 30px);
  text-decoration: underline;
}

.underline-intro {
  @include typography(1.125, 400, 27px);
  text-decoration: underline;
}

.underline-body {
  @include typography(1.125, 400, 27px);
  text-decoration: underline;
}

.underline-caption {
  @include typography(0.875, 400, 21px);
  text-decoration: underline;
}

.underline-micro {
  @include typography(0.75, 400, 18px);
  text-decoration: underline;
}

// Bold-Underline
.bold-underline-giant {
  @include typography(3, 700, 72px);
  text-decoration: bold-underline;
}

.bold-underline-h1 {
  @include typography(2, 700, 48px);
  text-decoration: bold-underline;
}

.bold-underline-h1-tablet {
  @include typography(1.75, 700, 42px);
  text-decoration: bold-underline;
}

.bold-underline-h2 {
  @include typography(1.75, 700, 42px);
  text-decoration: bold-underline;
}

.bold-underline-h2-tablet {
  @include typography(1.5, 700, 36px);
  text-decoration: bold-underline;
}

.bold-underline-h3 {
  @include typography(1.5, 700, 36px);
  text-decoration: bold-underline;
}

.bold-underline-h3-tablet {
  @include typography(1.25, 700, 30px);
  text-decoration: bold-underline;
}

.bold-underline-h4 {
  @include typography(1.25, 700, 30px);
  text-decoration: bold-underline;
}

.bold-underline-intro {
  @include typography(1.125, 700, 27px);
  text-decoration: bold-underline;
}

.bold-underline-body {
  @include typography(1.125, 700, 27px);
  text-decoration: bold-underline;
}

.bold-underline-caption {
  @include typography(0.875, 700, 21px);
  text-decoration: bold-underline;
}

.bold-underline-micro {
  @include typography(0.75, 700, 18px);
  text-decoration: bold-underline;
}
