import { Injectable } from '@angular/core';
import { HttpRe<PERSON>, HttpHandler, HttpEvent, HttpInterceptor, HttpErrorResponse } from '@angular/common/http';
import { catchError, EMPTY, Observable, skip, switchMap, throwError } from 'rxjs';
import { AuthenticationService } from './authentication.service';
import { NotificationService, NotificationVariant } from '@gfk/ng-lib';
import { AuthRoute } from '../../constants';
import { Router } from '@angular/router';

@Injectable()
export class AuthenticationInterceptor implements HttpInterceptor {
	errorMessageJustSent = false;
	constructor(private readonly authService: AuthenticationService, private readonly notificationService: NotificationService,private router: Router) {}

	intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
		if (this.authService.loginInProcess && !request.url.includes(AuthRoute.SIGNIN)) {
			return this.authService.loginInProcess$.pipe(
				skip(1),
				switchMap(() => this.handleBasicRequest(request, next))
			);
		} else {
			return this.handleBasicRequest(request, next);
		}
	}

	private authenticate(): Observable<boolean> {
		return this.authService.authenticate();
	}

	private handleBasicRequest = (request: HttpRequest<unknown>, next: HttpHandler) =>
    next.handle(request).pipe(
        catchError((error) => {
            return this.handleAuthenticationError(error, request, next);
        })
    );

	private handleAuthenticationError = (error: any, request: HttpRequest<unknown>, next: HttpHandler) => {
		if (error instanceof HttpErrorResponse && error.status === 401) {
			return this.authenticate().pipe(switchMap((wasSuccess) => (wasSuccess ? this.retryRequest(request, next) : this.abortRequest())));
		}
		return throwError(() => error);
	};

	private retryRequest = (request: HttpRequest<unknown>, next: HttpHandler) => next.handle(request);

	private abortRequest = (): Observable<never> => {
		if (!this.errorMessageJustSent) {
			this.notificationService?.errorAlert({
				title: 'Login failed',
				message: 'Are you a StarTrack User and connected via VPN?',
				variant: NotificationVariant.COMPACT,
			});
			this.errorMessageJustSent = true;
			setTimeout(() => (this.errorMessageJustSent = false), 25);
		}
		return EMPTY;
	};
}
