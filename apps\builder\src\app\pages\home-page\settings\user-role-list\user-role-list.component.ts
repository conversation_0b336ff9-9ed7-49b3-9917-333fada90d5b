import { ChangeDetector<PERSON><PERSON>, Component, EventEmitter, OnInit, Output, QueryList, ViewChildren, computed, inject } from '@angular/core';
import { SettingsConstants } from '@builder/shared/settings.constants';
import { EdsTr, EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { Observable, Subject, map } from 'rxjs';
import { FilterService, FilterRecords, Filter } from '@dwh/dx-lib/src';
import { BannerService } from '@builder/shared/services/banner.service';
import { HasMultiSelectTable } from '@builder/shared/components/has-multi-select-table/has-multi-select-table';
import { FormBuilder, FormGroup } from '@angular/forms';
import { RadioLabelsModel } from '@gfk/ng-lib/lib/components/radio-buttons/models/radio-buttons.interface';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { PutApiService } from '@builder/shared/services/put-api.service';
import * as moment from 'moment-timezone';
import { EntityId } from '@ngrx/signals/entities';

export interface PageChange {
	totalPages: number;
	pageIndex: number;
	pageSize: number;
	previousPageIndex: number;
}

export interface AutocompleteOption {
	value: string;
	label: string;
}
@Component({
	selector: 'dx-user-role-list',
	templateUrl: './user-role-list.component.html',
	styleUrls: ['./user-role-list.component.scss']
})
export class UserRoleListComponent extends HasMultiSelectTable<any> implements OnInit {
	@ViewChildren(EdsTr) _allRows!: QueryList<EdsTr>;
	allRows = computed(() => this.filterBodyRows(this._allRows.toArray()));
	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);
	
	@ViewChildren(EdsTr) selectedRows!: QueryList<EdsTr>;

	userRoleLabelGroup: RadioLabelsModel = {
		label: 'User Role'
	};

	CountryLabelGroup: RadioLabelsModel = {
		label: 'Countries'
	};


	userRoleOptions: { title: string; inputClass: string; name: string; value: string; hasError: boolean; checked: boolean; disabled: boolean; }[] = [];

	readonly date_with_time = SettingsConstants.DATE_FORMAT_WITHOUT_TIME;
	icon = 'help';
	userRoleData!: any;
	visibleUserRole: any[] = [];
	resetForm: Subject<boolean> = new Subject();
	FilterTrayOpenFlag = false;
	filterCount = '';
	@Output() isShownFilterTray = new EventEmitter<any>();
	selectedFilters: any;
	isLoading = false;
	chips: any = [];
	inputValue = '';
	errorIcon = 'error';
	paginatedUserRole: any[] = [];
	acceptConfirmationModal!: boolean;
	declineConfirmationModal!: boolean;
	editConfirmationModal!: boolean;
	revokeConfirmationModal!: boolean;
	requestRoleConfirmationModal!: boolean;
	assignedCountriesDetailModal!: boolean;
	isBannerVisible: any;
	pageSize: any;
	pageSizeForCountryList: any;
	currentPage = 1;
	currentPageForCountryList = 1;
	readonly defaultPageSize = 10;
	readonly defaultPageSizeforCountryList = 5;
	readonly pageSizeOptions: number[] = [10, 25, 50, 100];
	readonly pageSizeOptionsForCountryList: number[] = [5];
	showTray!: boolean;
	filterApplied!: boolean;
	userRoleFormGroup!: FormGroup;
	countryGroup!: FormGroup;
	optionOneHoverText = 'Authorized to manage QC Periods and QC Projects within assigned countries, with full viewing rights.';
	optionTwoHoverText = 'Authorized to manage QC Periods, QC Projects, QC Security, and MDQCA user requests within assigned countries, with full viewing rights.';
	optionThreeHoverText = 'Full administrative rights, including management of all projects, user requests and everything in Builder X.';
	userRoles$!: Observable<AutocompleteOption[]>;
	userRolesList: any = [];
	loggedInUserRole: any;
	emptyUserPermission = false;
	enableRequestBtn = true;
	disableActionBtn!: boolean;
	disableEditBtn!: boolean;
	disableRevokeBtn!: boolean
	disableAddBtn = true;
	disableAcceptBtn=false;
	getFilterData: Subject<boolean> = new Subject();
	showFilterTrayBtn = true;
	pendingUserPermission = false;
	deniedUserPermission = false;
	countries$!: Observable<AutocompleteOption[]>;
	countriesList: any = [];
	selectedItem: any;
	actualItem: any;
	MIPSelected = false;
	filteredCountryList: any = [];
	countryListData: any;
	visibleCountryList: any[] = [];
	uniquePendingCountries: any[] = [];
	loadTableData!: boolean;
	timezone = moment.tz.guess();
	countryListDataId =0;
	filterCountriesList: any=[];
	setPageNo: any;

	getId({ id }: any): number {
		return id;
	}

	constructor(
		public filterService: FilterService,
		private bannerService: BannerService,
		protected cdRef: ChangeDetectorRef,
		private formBuilder: FormBuilder,
		private getApiService: GetApiService,
		private postApiService: PostApiService,
		private putApiService: PutApiService,
	) {
		super(cdRef);
		this.pageSize = this.defaultPageSize;
		this.pageSizeForCountryList = this.defaultPageSizeforCountryList;
	}

	ngOnInit(): void {
		this.getCountryList();
		this.bannerService.isVisible$.subscribe((response: any) => {
			this.isBannerVisible = response;
		});
		this.userRoleFormInitialization();
	}

	hasPendingStatus(userRoles: any[]): boolean {
		const userData = JSON.parse(localStorage.getItem('user')!);
		const userEmail = userData.email.toLowerCase();
		return userRoles.some(userRole => userRole.status === 'Pending' && userRole.Email !== userEmail && !userRole.selfPermission);
	}

	shouldDisplaySelector(): boolean {
		return (this.loggedInUserRole?.name === 'Master' ||
			this.loggedInUserRole?.name === 'Account Manager') &&
			this.hasPendingStatus(this.visibleUserRole);
	}

	selectRowsWithConditions(event: Event, allEntities: any[], loggedInUserRole: any) {
		if (!(event instanceof CustomEvent) || !allEntities || allEntities.length === 0) {
			return;
		}
		const { detail } = event as CustomEvent<EntityId[]>;
		this.selected().clear();
		const allSelectedMap = new Map<EntityId, any>();
		detail.forEach((id) => {
			const entity = allEntities.find((entity) => this.getId(entity) === id);
			if (entity) {
				const isMasterCondition = loggedInUserRole.name === 'Master' && !entity.selfPermission && (entity.status === 'Pending' || entity.status === 'Approved');
				const isAccountManagerCondition = loggedInUserRole.name === 'Account Manager' && !entity.selfPermission && entity.roleName === 'MarketData QC' && entity.countryStatus === 'Pending';
				if (isMasterCondition || isAccountManagerCondition) {
					allSelectedMap.set(this.getId(entity), entity);
				}
			}
		});
		this.selected.set(allSelectedMap);
	}

	getLoggedInUserRole() {
		this.getApiService.getLoggedInUserRole()
			.subscribe({
				next: (result: any) => {
					this.handleLoggedInUserRoleSuccess(result);
				},
				error: (error) => {
					this.handleLoggedInUserRoleError(error);
				}
			});
	}
	
	handleLoggedInUserRoleSuccess(result: any) {
		
		// Flatten countryIds from the response
		const allowedCountryIds = result
		.flatMap(role => role.countries)
		.filter(country => country.status === 'Approved')
		.map(country => country.countryId);

		if(allowedCountryIds.length>0){
		// Filter countriesList to only include matching country IDs
		this.filterCountriesList = this.countriesList.filter(country =>
			allowedCountryIds.includes(parseInt(country.value))
			);
		}
		else{
			this.filterCountriesList = this.countriesList;
		}

		this.enableRequestBtn = result.every((userRole) => userRole.status !== 'Pending');
		if (!result.length) {
			this.setPermissions(false, false, true);
		}
		if (result.length == 1){
			this.handleSingleSuccessResult(result);
		}
		else if (result.length > 1) {
			this.handleMultipleSuccessResult(result)
		}
		if (this.loggedInUserRole){
			this.updateFilterTrayVisibility();
			this.handleUserRolesList();
		}
	}
	
	handleSingleSuccessResult(result: any){
		result.forEach((item: any) => {
			if (item.status == 'Approved') {
				this.loggedInUserRole = item;
				this.setPermissions(false, false, false);
			}
			else if (item.status == 'Pending') {
				this.setPermissions(false, true, false);
			}
		});
	}
	
	handleMultipleSuccessResult(result: any){
		result.forEach((item: any) => {
			if (item.status == 'Approved') {
				this.loggedInUserRole = item;
				this.setPermissions(false, false, false);
			}
		});
	}
	
	
	setPermissions(empty: boolean, pending: boolean, denied: boolean) {
		this.emptyUserPermission = empty;
		this.pendingUserPermission = pending;
		this.deniedUserPermission = denied;
	}
	
	updateFilterTrayVisibility() {
		if (this.loggedInUserRole.name === 'Master' || this.loggedInUserRole.name === 'Account Manager') {
			this.showFilterTrayBtn = true;
			this.getFilterData.next(true);
		} else {
			this.showFilterTrayBtn = false;
		}
	}
	
	handleUserRolesList() {
		let roleId!: any;
		this.userRolesList.forEach((item: any) => {
			if (item.label == this.loggedInUserRole?.name) {
				roleId = [parseInt(item.value)];
			}
		});
		const userData = JSON.parse(localStorage.getItem('user')!);
		const userEmail = [userData.email.toLowerCase()];
		if (roleId == 3 || roleId == 4) {
			this.getUserRolesList(userEmail);
		}
		else {
			this.getUserRolesList();
		}
	}
	
	handleLoggedInUserRoleError(error: any) {
		let title: string;
		if ([400, 401, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to fetch User Role. Please contact administrator.';
			this.notifyWidget(title, 'error');
		} 
		else if (error.status == 404) {
			this.emptyUserPermission = true;
		}
	}
	

	getUserRoles() {
		this.userRolesList = [];
		this.userRoleOptions = [];
		this.userRoles$ = this.getApiService.getAsyncUserRoles()
			.pipe(
				map((userRoles: any) => userRoles.map((userRole: any) => ({ label: userRole.name, value: (userRole.id).toString() } as AutocompleteOption)))
			);
		this.userRoles$.subscribe({
			next: (result) => {
				this.userRolesList = result;
				this.getLoggedInUserRole();
				this.userRolesList.forEach((item: any) => {
					this.userRoleOptions.push(
						{ title: item.label, inputClass: '', name: 'radioGroup', value: item.value, hasError: false, checked: false, disabled: false }
					);
				});
			}
		});
	}

	confirmApply(pageNo: number) {
		this.setPageNo = pageNo;
		this.CreateFilterSummaryChips();
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		this.SetFiltersCount();
		this.setChips();
		if (count > 0 || this.inputValue) {
			this.getUserRolesList();
		} else {
			if (confirm('Loading results without any filters selected will take some time to load. Are you sure you want to proceed?')) {
				this.getUserRolesList();
			}
		}
	}

	createPayload(userEmail?: any, roleId?: any) {
		let roleIdValue: any;
		let emailValue: any;
		if(roleId){
			roleIdValue = [roleId];
		}
		else{
			roleIdValue = this.selectedFilters ? this.selectedFilters?.userRoleRequested : [];
		}
		if(userEmail){
			emailValue = userEmail;
		}
		else{
			emailValue = this.selectedFilters ? this.selectedFilters?.email : [];
		}
		return {
			"roleId": roleIdValue,
			"email": emailValue,
			"status": this.selectedFilters ? this.selectedFilters?.status : [],
			"countryIds": this.selectedFilters ? this.selectedFilters?.userRoleCountry : []
		};
	}

	getUserRolesList(userEmail?: any, roleId?: any) {
		this.loadTableData = true;
		const payload = this.createPayload(userEmail, roleId);
		this.showTray = false;
		this.postApiService.getUserRoleList(payload).subscribe({
			next: (result) => this.handleUserRoleListResponse(result),
			error: (error) => this.handleUserRoleListError(error)
		});
	}
	
	handleUserRoleListResponse(result: any) {
		this.userRoleData = [];
		this.visibleUserRole = [];
		const userData = JSON.parse(localStorage.getItem('user')!);
		const userEmail = userData.email.toLowerCase();
		const data: any = [];
		if (this.selectedFilters?.userRoleCountry.length>0) {
		result.records = result.records
			.map((item: any) => {
				item.countries = item.countries?.filter(c =>
					this.selectedFilters.userRoleCountry.includes(c.countryId.toString())
				);
				return item;
			})
			.filter(item => item.countries && item.countries.length > 0);
		}
		if (result.moreRecordsAvailable) {
			const title = 'More than 1000 results exist!';
			this.notifyWidget(title, 'info');
		}	
		result.records.forEach((item: any) => {
			item.countryStatus = this.getCountryStatus(item);
			item.selectedCountries = this.getSelectedCountriesList(item);
			item.name = item.userEmail.split('@')[0];
			if (item.name.includes('.')) {
				item.firstName = item.name.split('.')[0].charAt(0).toUpperCase() + item.name.split('.')[0].slice(1);
				item.lastName = item.name.split('.')[1].charAt(0).toUpperCase() + item.name.split('.')[1].slice(1);
			}
			else {
				item.firstName = item.name;
				item.lastName = '';
			}
			item.selfPermission = this.setSelfPermissionForUserRoleList(item, userEmail);
			item.updatedOn = this.formatUpdatedOn(item.updatedOn.toString());
			data.push(item);
		});
		this.userRoleData = data;
		if(this.setPageNo){
			this.currentPage = this.setPageNo;
			this.setPageNo = null;
		}
		this.visibleUserRole = this.getPageUserRoles(this.currentPage, this.pageSize);
		this.visibleUserRole.forEach(userRole => {
			if(userRole.countries.length>0){
            const countries = userRole.countries || [];
            const approved = countries.filter(c => c.status === 'Approved');
            const pending = countries.filter(c => c.status === 'Pending');
            userRole.approvedCountryCount = approved.length;
            userRole.pendingCountryCount = pending.length;
            userRole.approvedCountries = this.getSelectedCountriesListByStatus(userRole, 'Approved');
            userRole.pendingCountries = this.getSelectedCountriesListByStatus(userRole, 'Pending');
			}
        });
		this.cdRef.markForCheck();
		this.loadTableData = false;
		if (this.selectedFilters) {
			this.filterApplied = true;
		}
	}
	
	getCountryStatus(item: any) {
		let countryStatus: any;
		if (item.roleName === 'Master') {
			countryStatus = '';
		}
		else{
			if (item.status === 'Revoked') {
				countryStatus = 'Declined';
			}
			else if (item.countries.find(country => country.status == 'Pending')) {
				countryStatus = 'Pending';
			}
			else if (item.countries.every(country => country.status == 'Declined')) {
				countryStatus = 'Declined';
			}
			else{
				countryStatus = item.status;
			}
		}
		return countryStatus;
	}
	
	getSelectedCountriesList(item: any){
		const selectedCountriesList: any = [];
		item.countries.forEach((selectedCountries: any) => {
			this.countriesList.forEach((country: any) => {
				if (parseInt(country.value, 10) == selectedCountries.countryId) {
					selectedCountriesList.push(country.label);
				}
			});
		});
		return selectedCountriesList.sort().toString().replaceAll(',', ', ');	 
	}
	
	setSelfPermissionForUserRoleList(item: any, userEmail: any){
		let permission: any
		if (item.userEmail == userEmail) {
			permission = true;
		}
		else {
			permission = false;
		}
		return permission;
	}
	
	formatUpdatedOn(updatedOn: any) {
		const date = new Date(updatedOn);
		const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, date);
		return moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm') + " " + timeZoneAbbreviation;
	}
	
	handleUserRoleListError(error: any) {
		let title: string;
		if (error.status == 404) {
			this.userRoleData = null;
			this.visibleUserRole = [];
			if (this.selectedFilters) {
				this.filterApplied = true;
			}
		} 
		else if ([400, 401, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to fetch User Role List. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
	}
	

	openRequestRoleConfirmationModal() {
		this.requestRoleConfirmationModal = true;
		this.userRoleOptions.forEach((option: any) => {
			if(this.loggedInUserRole && this.loggedInUserRole.name === option.title){
				this.userRoleFormGroup?.get('userRole')?.setValue(option.value);
				option.checked = true;
				this.countryChangeEvent(parseInt(option.value))
			}
		});
	}

	openAcceptConfirmationModal() {
		this.acceptConfirmationModal = true;
		this.declineConfirmationModal = false;
		this.editConfirmationModal = false;
		this.revokeConfirmationModal = false;
	}

	openDeclineConfirmationModal() {
		this.declineConfirmationModal = true;
		this.acceptConfirmationModal = false;
		this.editConfirmationModal = false;
		this.revokeConfirmationModal = false;
	}

	openEditConfirmationModal() {
		this.filteredCountryList = [];
		if(this.loggedInUserRole?.name === 'Account Manager'){
			// For Account Managers: Show their assigned countries but exclude countries already approved for the selected user
			const accountManagerAssignedCountries = this.loggedInUserRole.countries
				.filter(country => country.status === 'Approved')
				.map(country => country.countryId);

			this.filteredCountryList = this.countriesList.filter(country => {
				// Include only countries that the Account Manager is assigned to
				const isAssignedToAM = accountManagerAssignedCountries.includes(parseInt(country.value));
				if (!isAssignedToAM) {
					return false;
				}

				// Exclude countries that are already approved for the selected user
				const matchingItemInCountriesList = this.selectedEntities[0].countries.find(item1 => item1.countryId === parseInt(country.value));
				if (matchingItemInCountriesList && matchingItemInCountriesList.status === "Approved") {
					return false;
				}

				return true;
			});
		}
		else{
			this.filteredCountryList = this.countriesList.filter(item2 => {
				const matchingItemInCountriesList = this.selectedEntities[0].countries.find(item1 => item1.countryId === parseInt(item2.value));
				if (!matchingItemInCountriesList) {
				  return true;
				}
				if (matchingItemInCountriesList.status !== "Approved") {
				  return true;
				}
				  return false;
			});
		}
		this.disableAcceptBtn = this.selectedItem.countries.length==0;
		const selectedCountry: number[] = [];
		this.selectedItem.countries.forEach((country: any) => {
			if(country.status == 'Pending'){
				selectedCountry.push(country.countryId.toString());
			}
		});
		this.countryGroup.get('Country')?.setValue(selectedCountry);
		this.editConfirmationModal = true;
		this.declineConfirmationModal = false;
		this.acceptConfirmationModal = false;
		this.revokeConfirmationModal = false;
	}

	openRevokeConfirmationModal() {
		this.declineConfirmationModal = false;
		this.acceptConfirmationModal = false;
		this.editConfirmationModal = false;
		this.revokeConfirmationModal = true;
	}

	requestUserRoles(confirmsave?: boolean) {
		if (!confirmsave) {
			this.handleInitialUserRoleRequest();
		} 
		else {
			this.loadTableData = true;
			const selectedUserRoleName = this.getSelectedUserRoleName();
			if (this.isUserRoleUpdateAllowed(selectedUserRoleName)) {
				this.requestUpdateUserRole();
			} else {
				this.requestNewUserRole();
			}
		}
	}
	
	handleInitialUserRoleRequest() {
		this.getUserRoles();
		this.userRoleFormInitialization();
		this.requestRoleConfirmationModal = false;
		this.MIPSelected = false;
	}
	
	getSelectedUserRoleName() {
		const roleValue = this.userRoleFormGroup.get('userRole')?.value;
		let roleName: any;
		if(roleValue == 1){
			roleName = 'Master';
		}
		else if(roleValue == 2){
			roleName = 'Account Manager';
		}
		else{
			roleName = 'MarketData QC';
		}
		return roleName;
	}
	
	isUserRoleUpdateAllowed(selectedUserRoleName: any) {
		return this.loggedInUserRole && this.loggedInUserRole.name === selectedUserRoleName && selectedUserRoleName !== 'Master';
	}
	
	requestUpdateUserRole() {
		const requestedCountries = this.getRequestedCountries();
		const payload = {
			statusId: 0,
			requestDetails: [
				{
					requestId: this.loggedInUserRole.id,
					requestCountries: requestedCountries || []
				}
			]
		};
		this.putApiService.updateUserRoles(payload).subscribe({
			next: () => this.handleRequestUserRoleSuccess(),
			error: (error) => this.handleRequestUserRoleError(error),
			complete: () => this.handleRequestUserRoleComplete()
		});
	}
	
	getRequestedCountries() {
		const requestedCountries: any = [];
		this.userRoleFormGroup.get('RoleCountry')?.value.forEach((country: any) => {
			requestedCountries.push({
				statusId: 1,
				countryIds: parseInt(country)
			});
		});
		return requestedCountries;
	}
	
	requestNewUserRole() {
		const payload = {
			roleId: parseInt(this.userRoleFormGroup.get('userRole')?.value),
			countryIds: this.userRoleFormGroup.get('RoleCountry')?.value
				? this.userRoleFormGroup.get('RoleCountry')?.value.map((id: string) => parseInt(id, 10))
				: []
		};
		this.postApiService.requestUserRoles(payload).subscribe({
			next: () => this.handleRequestUserRoleSuccess(),
			error: (error) => this.handleRequestUserRoleError(error),
			complete: () => this.handleRequestUserRoleComplete()
		});
	}
	
	handleRequestUserRoleSuccess() {
		const title = 'User Role Requested';
		this.notifyWidget(title, 'success');
		this.requestRoleConfirmationModal = false;
		this.loadTableData = false;
	}
	
	handleRequestUserRoleError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = `Error: ${error.status}. Something went wrong. Please contact administrator.`;
		} 
		else {
			title = error?.message == null ? error?.error : error?.message;
		}
		this.notifyWidget(title, 'error');
		this.requestRoleConfirmationModal = false;
		this.loadTableData = false;
	}
	
	handleRequestUserRoleComplete() {
		this.requestRoleConfirmationModal = false;
		this.getLoggedInUserRole();
		this.getUserRoles();
		this.userRoleFormInitialization();
		this.MIPSelected = false;
		this.loadTableData = false;
	}

	editSelectedUserRoles(confirmsave?: boolean) {
		if (!confirmsave) {
			this.closeAcceptAndEditRequestModals();
		} 
		else {
			this.loadTableData = true;
			const userEmail = this.getUserEmail();
			const requestDetails = this.prepareRequestDetails(userEmail);
			this.putApiService.updateUserRoles({
				statusId: 2,
				requestDetails: requestDetails
			})
			.subscribe({
				next: () => this.handleEditUserRoleSuccess(),
				error: (error) => this.handleEditUserRoleError(error),
				complete: () => this.handleEditUserRoleComplete()
			});
		}
	}
	
	closeAcceptAndEditRequestModals() {
		this.acceptConfirmationModal = false;
		this.editConfirmationModal = false;
	}
	
	getUserEmail() {
		const userData = JSON.parse(localStorage.getItem('user')!);
		return userData.email.toLowerCase();
	}
	
	prepareRequestDetails(userEmail: string) {
		const countriesToApprove:any = [];
		const countriesToDecline:any = [];
		const requestDetails:any = [];
		this.selectedEntities.forEach((item: any) => {
			if (item.userEmail !== userEmail) {
				const matchedCountries = item.countries.filter(itemCountry =>
					this.countryGroup.get("Country")?.value.some(selectedCountry => itemCountry.countryId === parseInt(selectedCountry) && itemCountry.status === 'Pending')
				);
				const newCountries = this.countryGroup.get("Country")?.value.filter(selectedCountry =>
					!matchedCountries.some(matchedCountry => parseInt(selectedCountry) === matchedCountry.countryId)
				);
				const uniqueItemCountries = item.countries.filter(itemCountry =>
					!this.countryGroup.get("Country")?.value.some(selectedCountry => itemCountry.countryId === parseInt(selectedCountry))
				);
				matchedCountries.forEach((country: any) => {
					if(country.status === 'Pending'){
						countriesToApprove.push({
							statusId: 2,
							countryIds: country.countryId || []
						})
					}
				});
				newCountries.forEach((countryId: any) => {
					countriesToApprove.push({
						statusId: 2,
						countryIds: parseInt(countryId) || []
					})
				});
				uniqueItemCountries.forEach((country: any) => {
					if(country.status === 'Pending'){
						countriesToDecline.push({
							statusId: 3,
							countryIds: country.countryId || []
						})
					}
				});
				const payload = {
					requestId: item.id,
					requestCountries: (countriesToApprove.length || countriesToDecline.length) ? [...countriesToApprove, ...countriesToDecline] : []
				}	
				requestDetails.push(payload);
			}
		});
		return requestDetails;
	}
	
	handleEditUserRoleSuccess() {
		this.notifyWidget('User role(s) were successfully changed.', 'success');
		this.closeAcceptAndEditRequestModals();
		this.loadTableData = false;
	}
	
	handleEditUserRoleError(error: any) {
		const title = this.getEditUserRoleErrorMessage(error);
		this.notifyWidget(title, 'error');
		this.acceptConfirmationModal = false;
		this.loadTableData = false;
	}
	
	getEditUserRoleErrorMessage(error: any) {
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			return `Error: ${error.status}. Something went wrong. Please contact administrator.`;
		}
		return error?.message == null ? error?.error : error?.message;
	}
	
	handleEditUserRoleComplete() {
		this.clearSelection();
		this.closeAcceptAndEditRequestModals();
		this.getLoggedInUserRole();
		this.getUserRolesList();
		this.loadTableData = true;
	}

	acceptSelectedUserRoles(confirmsave?: boolean) {
		if (!confirmsave) {
			this.closeAcceptAndEditRequestModals();
		} 
		else {
			this.loadTableData = true;
			this.processSelectedUserRoles();
		}
	}
	
	processSelectedUserRoles() {
		const userEmail = this.getUserEmail();
		const requestDetails = this.handleRequestDetailsValue(userEmail);
		if (requestDetails.length > 0) {
			this.handleAcceptUserRoleSubcription(requestDetails);
		}
	}
	
	handleRequestDetailsValue(userEmail: any) {
		const requestDetails: any = [];
		this.selectedEntities.forEach((item: any) => {
			if (item.userEmail !== userEmail) {
				let payload: any;
				if(item.roleId != 1){
					item.pendingCountries = [];
					item.countries?.forEach((country: any) => {
						if(country.status == 'Pending'){
							item.pendingCountries.push({
								statusId: 2,
								countryIds: country.countryId
							})
						}
					});
					payload = {
						requestId: item.id,
						requestCountries: item.pendingCountries || []
					}	
				}
				else{
					payload = {
						requestId: item.id
					}	
				}
				requestDetails.push(payload);
			}
		});
		return requestDetails;
	}
	
	handleAcceptUserRoleSubcription(requestDetails: any[]) {
		this.putApiService.updateUserRoles({
			statusId: 2,
			requestDetails: requestDetails
		})
		.subscribe({
			next: (response: any) => {
				if(response[0].roleId == 1){
					const title = 'User Role Updated Successfully';
					const message = 'Approved user(s) are currently being synced in the background to the security list of all base projects. This process will complete in a few minutes.'
					this.notifyWidget(title, 'success', message);
				}
				else{
					this.notifyWidget('User role(s) were successfully changed.', 'success');
				} 
				this.loadTableData = false;
			},
			error: (error) => this.handleAcceptUserRoleError(error),
			complete: () => this.handleAcceptUserRoleComplete()
		});
	}
	
	handleAcceptUserRoleError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = `Error: ${error.status}. Something went wrong. Please contact administrator.`;
		} else {
			title = error?.message == null ? error?.error : error?.message;
		}
		this.notifyWidget(title, 'error');
		this.closeAcceptAndEditRequestModals();
		this.loadTableData = false;
	}
	
	handleAcceptUserRoleComplete() {
		this.loadTableData = false;
		this.handleEditUserRoleComplete();
	}

	declineSelectedUserRoles(confirmsave?: boolean) {
		if (!confirmsave) {
			this.closeDeclineModal();
		} 
		else {
			this.loadTableData = true;
			const userEmail = this.getUserEmail();
			const requestDetails = this.prepareDeclineRequestDetails(userEmail);
			this.handleDeclineRoleSubscription(requestDetails);
		}
	}
	
	closeDeclineModal() {
		this.declineConfirmationModal = false;
	}
	
	prepareDeclineRequestDetails(userEmail: string) {
		const requestDetails: any = [];
		this.selectedEntities.forEach((item: any) => {
			
			if (item.userEmail !== userEmail) {
				let payload: any;
				if(item.roleId != 1){
					item.pendingCountries = [];
					item.countries?.forEach((country: any) => {
						if(country.status == 'Pending'){
							country.status = 'Declined'
							item.pendingCountries.push({
								statusId: 3,
								countryIds: country.countryId
							})
						}
					});
					payload = {
						requestId: item.id,
						requestCountries: item.pendingCountries || []
					}	
				}
				else{
					payload = {
						requestId: item.id
					}	
				}
				requestDetails.push(payload);
			}
		});
		this.selectedEntities.forEach((item: any) => {
			if (item.countries.find(country => country.status == 'Approved')) {
				item.status = "Approved";
			}
			else if (item.countries.find(country => country.status == 'Pending')) {
				item.status = "Pending";
			}
			else if (item.countries.find(country => country.status == 'Revoked')) {
				item.status = "Revoked";
			}
			else{
				item.status = "Declined";
			}
		})
		
		return requestDetails;
	}
	
	handleDeclineRoleSubscription(requestDetails: any) {
		this.putApiService.updateUserRoles({
			statusId: 3,
			requestDetails: requestDetails
		})
		.subscribe({
			next: () => { 
				this.notifyWidget('User role(s) were successfully changed.', 'success');
				this.loadTableData = false;
			},
			error: (error) => this.handleDeclineRequestError(error),
			complete: () => this.handleDeclineRequestComplete()
		});
	}
	
	handleDeclineRequestError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = `Error: ${error.status}. Something went wrong. Please contact administrator.`;
		} else {
			title = error?.message == null ? error?.error : error?.message;
		}
		this.notifyWidget(title, 'error');
		this.closeDeclineModal();
		this.loadTableData = false;
	}
	
	handleDeclineRequestComplete() {
		this.clearSelection();
		this.closeDeclineModal();
		this.getLoggedInUserRole();
		this.getUserRolesList();
		this.loadTableData = false;
	}

	revokeSelectedUserRoles(confirmsave?: boolean) {
		if (!confirmsave) {
			this.closeRevokeModal();
		} 
		else {
			this.loadTableData = true;
			const userEmail = this.getUserEmail();
			const requestDetails = this.prepareRevokeRequestDetails(userEmail);
			this.handleRevokeRoleSubscription(requestDetails);
		}
	}
	
	closeRevokeModal() {
		this.revokeConfirmationModal = false;
	}
	
	prepareRevokeRequestDetails(userEmail: string) {
		const requestDetails: any = [];
		this.selectedEntities.forEach((item: any) => {
			if (item.userEmail !== userEmail && item.status == 'Approved') {
				let payload: any;
				if(item.roleId != 1){
					item.pendingCountries = [];
					item.countries?.forEach((country: any) => {
						if(country.status !== 'Declined'){
							item.pendingCountries.push({
								statusId: 3,
								countryIds: country.countryId
							});
						}
					});
					payload = {
						requestId: item.id,
						requestCountries: item.pendingCountries || []
					}	
				}
				else{
					payload = {
						requestId: item.id
					}	
				}
				requestDetails.push(payload);
			}
		});
		return requestDetails;
	}
	
	handleRevokeRoleSubscription(requestDetails: any[]) {
		this.putApiService.updateUserRoles({
			statusId: 4,
			requestDetails: requestDetails
		})
		.subscribe({
			next: (response: any) => {
				if(response[0].roleId == 1){
					const title = 'User Role Updated Successfully';
					const message = 'Revoked user(s) are currently being removed in the background from the security list of all base projects. This process will complete in a few minutes.'
					this.notifyWidget(title, 'success', message);
				}
				else{
					this.notifyWidget('User role(s) were successfully changed.', 'success');
				}
				this.loadTableData = false;
			},
			error: (error) => this.handleRevokeRoleError(error),
			complete: () => this.handleRevokeRoleComplete()
		});
	}
	
	handleRevokeRoleError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = `Error: ${error.status}. Something went wrong. Please contact administrator.`;
		} else {
			title = error?.message == null ? error?.error : error?.message;
		}
		this.notifyWidget(title, 'error');
		this.closeRevokeModal();
		this.loadTableData = false;
	}
	
	handleRevokeRoleComplete() {
		this.clearSelection();
		this.closeRevokeModal();
		this.getLoggedInUserRole();
		this.getUserRolesList();
		this.loadTableData = false;
	}


	userRoleFormInitialization() {
		this.userRoleFormGroup = this.formBuilder.group({
			userRole: null,
			RoleCountry: null
		});
		this.countryGroup = this.formBuilder.group({
			Country: []
		});
	}

	getSelectedFilters(selectedFilters: any) {
		this.selectedFilters = selectedFilters;
	}

	getWidth() {
		this.FilterTrayOpenFlag = !this.FilterTrayOpenFlag;
		return '100%';
	}

	resetFilterTray() {
		this.resetForm.next(true);
		this.CreateFilterSummaryChips();
		this.SetFiltersCount();
	}

	public isShownFilterTrayEventHandler($event: boolean) {
		this.showTray = !this.showTray;
		this.FilterTrayOpenFlag = $event;
		const element = document.getElementById('sidebar-modal') as HTMLElement;
		if (this.isBannerVisible) {
			element.classList.add('sidebar-modal-color-with-banner');
			element.classList.remove('sidebar-modal-color-without-banner');
		}
		else {
			element.classList.remove('sidebar-modal-color-with-banner');
			element.classList.add('sidebar-modal-color-without-banner');
		}
	}

	onCloseFilterTray() {
		this.getWidth();
		this.showTray = false;
	}
	onOpenFilterTray() {
		this.showTray = true;
	}

	CreateFilterSummaryChips() {
		if (this.selectedFilters && Object.keys(this.selectedFilters).length) {
			this.emailFilterSummary();
			this.userRoleRequestedFilterSummary();
			this.statusFilterSummary();
			this.userRoleCountrySummary();
		}
	}

	SetFiltersCount() {
		let count;
		this.filterService.filtersSelected$.subscribe((selectedFilters: FilterRecords) => {
			count = Number(Object.keys(selectedFilters).length);
		});
		if (count > 0) {
			this.filterCount = '(' + count.toString() + ')';
		} else {
			this.filterCount = '';
		}
	}

	emailFilterSummary() {
		this.filterService.setFilters({
			panel: {
				name: 'Email',
				values: this.selectedFilters.email,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	userRoleRequestedFilterSummary() {
		this.filterService.setFilters({
			status: {
				name: 'User Role Requested',
				values: this.selectedFilters.userRoleRequested,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	statusFilterSummary() {
		this.filterService.setFilters({
			sector: {
				name: 'Status',
				values: this.selectedFilters.status,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	userRoleCountrySummary(){
		this.filterService.setFilters({
			country: {
				name: 'Country',
				values: this.selectedFilters.userRoleCountry,
				slice: window.location.href.split('/')[3],
			} as Filter,
		});
	}

	setChips() {
		this.chips = [];
		if (this.selectedFilters) {
			if (this.selectedFilters.email.length) {
				this.chips.push({ label: 'Email', value: this.selectedFilters.email.length });
			}
			if (this.selectedFilters.userRoleRequested.length) {
				this.chips.push({ label: 'Role Requested', value: this.selectedFilters.userRoleRequested.length });
			}
			if (this.selectedFilters.status.length) {
				this.chips.push({ label: 'Status', value: this.selectedFilters.status.length });
			}
			if (this.selectedFilters.userRoleCountry.length) {
				this.chips.push({ label: 'Country', value: this.selectedFilters.userRoleCountry.length });
			}
		}
	}

	onPageChangeUserRoles(event: PageChange) {
		this.currentPage = event.pageIndex;
		this.pageSize = event.pageSize;
		this.visibleUserRole = this.getPageUserRoles(this.currentPage, this.pageSize);
		this.visibleUserRole.forEach(userRole => {
			if(userRole.countries.length>0){
				const countries = userRole.countries || [];
				const approved = countries.filter(c => c.status === 'Approved');
				const pending = countries.filter(c => c.status === 'Pending');
				userRole.approvedCountryCount = approved.length;
				userRole.pendingCountryCount = pending.length;
				userRole.approvedCountries = this.getSelectedCountriesListByStatus(userRole, 'Approved');
				userRole.pendingCountries = this.getSelectedCountriesListByStatus(userRole, 'Pending');
			}
        });
		this.cdRef.markForCheck();
	}

	private calculatePageStart(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEnd(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

	private getPageUserRoles(page: number, size: number) {
		const start = this.calculatePageStart(page, size);
		const end = this.calculatePageEnd(page, size);
		return this.userRoleData.slice(start, end);
	}

	rowsSelectedHandler(event: any, userRoleData: any) {
		const selectedUserRoles = this.getSelectedUserRoles(event, userRoleData);
		this.updateButtonStates(selectedUserRoles);
	}
	
	getSelectedUserRoles(event: any, userRoleData: any) {
		const selectedUserRoles: any = [];
		userRoleData.forEach((item: any) => {
			event.detail.forEach((detailItem: any) => {
				if (detailItem == item.id) {
					selectedUserRoles.push(item);
					this.selectedItem = item;
					this.actualItem= item;
				}
			})
		});
		return selectedUserRoles;
	}
	
	updateButtonStates(selectedUserRoles: any[]) {
		if (selectedUserRoles.length == 1) {
			this.handleSingleSelection(selectedUserRoles);
		} 
		else if (selectedUserRoles.length > 1) {
			this.handleMultipleSelections();
		} 
	}
	
	handleSingleSelection(selectedUserRoles: any) {
		if (selectedUserRoles[0].roleName == 'Master') {
			this.handleMasterRole(selectedUserRoles);
		} 
		else {
			this.handleNonMasterRole(selectedUserRoles);
		}    
	}
	
	handleMasterRole(selectedUserRoles: any) {
		this.disableEditBtn = true;
		if(selectedUserRoles[0].status == 'Pending'){
			this.disableActionBtn = false;
			this.disableRevokeBtn = true;
		}
		else if(selectedUserRoles[0].status == 'Approved'){
			this.disableActionBtn = true;
			this.disableRevokeBtn = false;
		}
	}
	
	handleNonMasterRole(selectedUserRoles: any) {
		if(selectedUserRoles[0].status == 'Pending' && selectedUserRoles[0].countryStatus == 'Pending'){
			this.disableActionBtn = false;
			this.disableEditBtn = false;
			this.disableRevokeBtn = true;
		}
		else if(selectedUserRoles[0].status == 'Approved' && selectedUserRoles[0].countryStatus == 'Pending'){
			this.disableActionBtn = false;
			this.disableEditBtn = false;
			this.disableRevokeBtn = false;
		}
		else if(selectedUserRoles[0].status == 'Approved' && selectedUserRoles[0].countryStatus == 'Approved'){
			this.disableActionBtn = true;
			this.disableEditBtn = true;
			this.disableRevokeBtn = false;
		}
	}
	
	handleMultipleSelections() {
		this.disableEditBtn = true;
		let enableRevokeBtn = false;
		let enableAcceptDeclineBtn = false;
		enableRevokeBtn = this.selectedEntities.every((userRole) => (userRole.status == 'Approved'));
		enableAcceptDeclineBtn = this.selectedEntities.every((userRole) => (userRole.status == 'Pending'));
		if(enableRevokeBtn){
			this.disableRevokeBtn = false;
			this.disableActionBtn = true;
		}
		else if(enableAcceptDeclineBtn){
			this.disableRevokeBtn = true;
			this.disableActionBtn = false;
		}
		else{
			this.disableRevokeBtn = true;
			this.disableActionBtn = true;
		}
	}

	getCountryList() {
		this.countries$ = this.getApiService.getCountries()
			.pipe(
				map((countries: any) => countries.map((country: any) => ({ label: country.name, value: (country.id).toString(), isoName: country.isoName } as AutocompleteOption)))
			);
		this.countries$.subscribe({
			next: (result) => {
				this.countriesList = result;
				this.getUserRoles();
			}
		});
	}

	countryChangeEvent(event: any) {
		this.uniquePendingCountries = [];
		this.userRoleFormGroup?.get('RoleCountry')?.setValue([]);
		if (event != 1) {
			this.MIPSelected = true;
			this.disableAddBtn = true;
			const loggedInRoleSelected: any = this.userRoleOptions.find(option => option.value == event);
			if(this.loggedInUserRole && loggedInRoleSelected.title == this.loggedInUserRole.name){
				const loggedInApprovedCountries = this.loggedInUserRole.countries.filter(country => (country.status == 'Approved' || country.status == 'Pending'));
				const uniqueCountryList = this.countriesList.filter(obj1 => 
					!loggedInApprovedCountries.some(obj2 => parseInt(obj1.value) === parseInt(obj2.countryId))
				);
				const uniqueAssignedList = loggedInApprovedCountries.filter(obj2 => 
					!this.countriesList.some(obj1 => parseInt(obj2.countryId) === parseInt(obj1.value))
				);
				this.uniquePendingCountries = [...uniqueCountryList, ...uniqueAssignedList];
			}	
		}
		else {
			this.MIPSelected = false;
			this.disableAddBtn = false;
		}
	}

	changeCountryforSelected(event) {
		this.selectedEntities[0].countries = [];
		const countryIds = event.detail.map(item => parseInt(item as string, 10));
		this.selectedEntities[0].countries = countryIds;
		const selectedCountries: any = [];
		this.selectedEntities[0].countries = this.selectedEntities[0].countries.map(id => id.toString());
		selectedCountries.push(...this.selectedEntities[0].countries);
		this.countryGroup.get('Country')?.setValue(selectedCountries);
	}

	checkValues(event) {
		if (event.detail == null || event.detail.length == 0) {
			this.disableAddBtn = true;
		}
		else {
			this.disableAddBtn = false;
		}
	}

	checkAcceptValues(event) {
		if (event.detail == null || event.detail.length == 0) {
			this.disableAcceptBtn = true;
		}
		else {
			this.disableAcceptBtn = false;
		}
	}

	openAssignedCountriesDetailModal(countryList: any,requestId:number){
		countryList.forEach((country: any) => {
			this.countriesList.forEach((item: any) => {
				if(item.value == country.countryId){
					country.name = item.label
				}
			})
			const date = new Date(country.updatedOn.toString());
			const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, date);
			const cetDate = moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm') + " " + timeZoneAbbreviation;
			country.lastUpdatedOn = cetDate;
		});
		this.countryListData = countryList;
		this.countryListDataId = requestId;
		this.visibleCountryList = this.getPageCountryList(this.currentPageForCountryList, this.pageSizeForCountryList);
		this.assignedCountriesDetailModal = true;
	}

	closeAssignedCountriesDetailModal(){
		this.assignedCountriesDetailModal = false;
		this.currentPageForCountryList = 1;
	}

	onPageChangeCountryList(event: PageChange) {
		this.currentPageForCountryList = event.pageIndex;
		this.pageSizeForCountryList = event.pageSize;
		this.visibleCountryList = this.getPageCountryList(this.currentPageForCountryList, this.pageSizeForCountryList);
		this.cdRef.markForCheck();
	}

	private calculatePageStartForCountryList(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEndForCountryList(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

	private getPageCountryList(page: number, size: number) {
		const start = this.calculatePageStartForCountryList(page, size);
		const end = this.calculatePageEndForCountryList(page, size);
		return this.countryListData.slice(start, end);
	}

	getOffsetAtTime(timezone, date) {
		const zone = moment.tz.zone(timezone);
  
		if (zone) {
			const timestamp = moment(date).valueOf(); // Convert date to timestamp
			const abbrs = zone.abbrs; // Get the list of abbreviations
			const untils = zone.untils; // Get the list of timestamp changes (for daylight saving time changes)
			
			// Find the correct abbreviation based on the timestamp
			for (let i = 0; i < untils.length; i++) {
			if (timestamp < untils[i]) {
				return abbrs[i]; // Return abbreviation if timestamp is before the DST change
			}
			}
			
			// If no matching change is found (for times after the last DST change), use the last abbreviation
			return abbrs[abbrs.length - 1]; // Return the last abbreviation
		} else {
			return null; // Return null if the timezone is not found
		}
	}

	notifyWidget(title: string, notificationType: string, message?: string): void {
		if(notificationType == 'info'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if(notificationType == 'error'){
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else{
			this.notificationService.showNotification(title, {
				message: message || '',
				duration : 15000,
				variant: NotificationVariant.SUCCESS,
			});
		}
	}

	revokeCountry(requestId:number,countryId:number){
		this.revokeCountryRole(requestId,countryId);
	}

	revokeCountryRole(requestId:number,countryId:number) {
		const payload = {
			statusId: 0,
			requestDetails: [
				{
					requestId: requestId,
					requestCountries: [{
						statusId: 4,
						countryIds: countryId
					}]
				}
			]
		};
		this.putApiService.updateUserRoles(payload).subscribe({
			next: () => {
				this.notifyWidget('Country was successfully revoked.', 'success');
				this.loadTableData = false;
				const country = this.countryListData.find(c => c.countryId === countryId);
				if (country) {
					country.status = "Revoked";
				}
				const currentuserrole= this.visibleUserRole.find(c => c.id === this.countryListDataId);
				if(this.countryListData.every(country => country.status == 'Revoked')){
					currentuserrole.status = "Revoked";
					currentuserrole.countryStatus = "Revoked";
				}
				else if (this.countryListData.find(country => country.status == 'Approved')) {
					currentuserrole.status = "Approved";
				}
				else if (this.countryListData.find(country => country.status == 'Pending')) {
					currentuserrole.status = "Pending";
				}
				else if (this.countryListData.find(country => country.status == 'Revoked')) {
					currentuserrole.status = "Revoked";
					currentuserrole.countryStatus = "Revoked";
				}
			},
			error: (error) => this.handleRevokeRoleError(error)
		});
	}
	getSelectedCountriesListByStatus(item: any, status: string): string {
        const selectedCountriesList: string[] = [];
    
        item.countries
            ?.filter((c: any) => c.status === status)
            .forEach((selected: any) => {
                this.countriesList.forEach((country: any) => {
                    if (parseInt(country.value, 10) === selected.countryId) {
                        selectedCountriesList.push(country.label);
                    }
                });
            });
    
        return selectedCountriesList.sort().join(', ');
    }

}
