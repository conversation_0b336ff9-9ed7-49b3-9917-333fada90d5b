import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '@builder/shared/shared.module';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSortModule } from '@angular/material/sort';
import { NgLibModule } from '@gfk/ng-lib';
import { DxLibModule } from '@dwh/dx-lib/src';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BCRComponent } from './bcr.component';

const routes: Routes = [
	{
		path: "",
		children: [
			{
				path: ":id",
				component: BCRComponent,
				canActivate: [],
				canDeactivate: []
			},

		]
	  }
  ];


@NgModule({
	declarations: [BCRComponent],
	imports: [
		CommonModule,
		SharedModule,
		RouterModule.forChild(routes),
		MatPaginatorModule,
		MatSortModule,
		MatAutocompleteModule,
		NgLibModule,
		DxLibModule,
		FormsModule,
		ReactiveFormsModule
	],
	exports: [RouterModule],
	schemas: [
		CUSTOM_ELEMENTS_SCHEMA
	]
})
export class BCRModule {}
