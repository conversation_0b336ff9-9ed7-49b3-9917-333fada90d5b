import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '@builder/shared/shared.module';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { NgLibModule } from '@gfk/ng-lib';
import { DxLibModule } from '@dwh/dx-lib/src';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AuthGuard } from '@builder/shared/guards/auth.guard';

const routes: Routes = [
	{
		path: 'home',
		canActivate: [AuthGuard],
		loadChildren: () =>import('@builder/pages/home-page/landing-page/landing-page.module').then((m) => m.LandingPageModule),
	},
	{
		path: 'base-projects',
		canActivate: [AuthGuard],
		loadChildren: () =>import('@builder/pages/home-page/base-projects/base-projects.module').then((m) => m.BaseProjectsModule),
	},
	{
		path: 'settings',
		canActivate: [AuthGuard],
		loadChildren: () =>import('@builder/pages/home-page/settings/settings.module').then((m) => m.SettingsModule),
	},
	{
		path: 'retailer-separation',
		canActivate: [AuthGuard],
		loadChildren: () =>import('@builder/pages/home-page/retailer-separation/retailer-separation.module').then((m) => m.RetailerSeparationModule),
	},
  {
		path: 'bcr',
		canActivate: [AuthGuard],
		loadChildren: () =>import('@builder/pages/home-page/Base-Channel-Rearrangements/bcr.module').then((m) => m.BCRModule),
	}
];

@NgModule({
	declarations: [],
	imports: [
		CommonModule,
		SharedModule,
		RouterModule.forChild(routes),
		MatPaginatorModule,
		MatSortModule,
		NgLibModule,
		DxLibModule,
		FormsModule,
		ReactiveFormsModule
	],
	exports: [RouterModule],
})
export class HomePageModule {}
