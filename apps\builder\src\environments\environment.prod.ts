import { EnvironmentModel } from './environment.model';

export const environment: EnvironmentModel = {
  production: false,
  dev: true,
  apiUrl: '',
  spriteIconsPath: 'assets/images/sprites/sprite.svg',
  environments:[
    {
      location:'http://localhost:4200/',
      enabled: true
    },
    {
      location:'https://builder-ui.t1.dwh.in.gfk.com',
      enabled: true
    },
    {
      location:'https://builder-ui.t5.dwh.in.gfk.com',
      enabled: true
    }
      ,
    {
      location:'https://builder-ui.t3.dwh.in.gfk.com',
      enabled: true
    }
      ,
    {
      location:'https://builder-ui.t4.dwh.in.gfk.com',
      enabled: true
    },
    {
      location:'https://builder-ui.prod.dwh.vpc1446.in.gfk.com',
      enabled: true
    }
  ],
  version: '0.1.0-DEK-6152-VersioningFunctionality.1-rc.1008'
};
