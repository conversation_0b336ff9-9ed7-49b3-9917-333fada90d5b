import { Injectable } from '@angular/core';
import {
    <PERSON>ttpRequest,
    HttpHandler,
    HttpEvent,
    HttpInterceptor
} from '@angular/common/http';
import { finalize, Observable } from 'rxjs';
import { LoaderService } from '../services/loader.service';

@Injectable()

export class LoaderInterceptor implements HttpInterceptor {
    private activeRequests = 0;
  
    constructor(private loaderService: LoaderService) {}
  
    intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
      this.activeRequests++;
      if (this.activeRequests === 1) {
        this.loaderService.show();
      }  
      return next.handle(request).pipe(
        finalize(() => {
          this.activeRequests--;
          if (this.activeRequests === 0) {
            this.loaderService.hide();
          }
        })
      );
    }
  }