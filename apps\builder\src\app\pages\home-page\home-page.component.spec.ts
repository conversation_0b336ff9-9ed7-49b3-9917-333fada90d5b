import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HomePageComponent } from './home-page.component';
import { ConfigService } from '@builder/shared/services/config.service';
import { Router, NavigationEnd } from '@angular/router';
import { Subject, of } from 'rxjs';
import { EdsNotificationService } from '@gfk/ng-lib';
import { BannerService } from '@builder/shared/services/banner.service';

describe('HomePageComponent', () => {
  let component: HomePageComponent;
  let fixture: ComponentFixture<HomePageComponent>;
  let configService: ConfigService;
  let router: Router;
  let bannerService: BannerService;
  let notificationService: EdsNotificationService;

  beforeEach(async () => {
    const configServiceMock = {
      getVersion: jest.fn().mockReturnValue('v1.0.0'),
      getConfigVariable: jest.fn().mockReturnValue(of({
        UNDER_MAINTAINANCE: false  // Mock response for getConfigVariable
      }))
    };

    const routerMock = {
      events: new Subject<any>(),
      navigate: jest.fn(),
    };

    const bannerServiceMock = {
      isVisible$: of(true),
    };

    await TestBed.configureTestingModule({
      declarations: [HomePageComponent],
      providers: [
        { provide: ConfigService, useValue: configServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: BannerService, useValue: bannerServiceMock },
        EdsNotificationService,
      ],
    }).compileComponents();

    configService = TestBed.inject(ConfigService);
    router = TestBed.inject(Router);
    bannerService = TestBed.inject(BannerService);
    notificationService = TestBed.inject(EdsNotificationService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HomePageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the version on init', () => {
    expect(component.version).toBe('v1.0.0');
  });

  it('should subscribe to router events and set showContent based on URL', () => {
    const navigationEndEvent = new NavigationEnd(1, '/not-redirect-web', '/not-redirect-web');
    (router.events as Subject<any>).next(navigationEndEvent);
    expect(component.showContent);

    const redirectEvent = new NavigationEnd(1, '/redirect-web', '/redirect-web');
    (router.events as Subject<any>).next(redirectEvent);
    expect(component.showContent).toBeUndefined();
  });

  it('should set displayUI to true after 1 second', (done) => {
    jest.useFakeTimers();
    component.ngOnInit();
    jest.advanceTimersByTime(1000);
    fixture.detectChanges();
    expect(component.displayUI).toBe(true);
    done();
  });

  it('should subscribe to bannerService.isVisible$ and set isBannerVisible', () => {
    expect(component.isBannerVisible).toBe(true);
  });

  it('should update currentTab on tabActive call', () => {
    component.tabActive('newTab');
    expect(component.currentTab).toBe('newTab');
    component.tabActive('newTab');
    expect(component.currentTab).toBe('');
  });

  it('should return correct margin top on getMarginTop call', () => {
    expect(component.getMarginTop(10)).toBe('35px');
  });

  it('should complete _destroying$ on ngOnDestroy', () => {
    const completeSpy = jest.spyOn(component['_destroying$'], 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should call getConfigVariable on init and set underMaintainance correctly', () => {
    // Mocked value from getConfigVariable
    component.ngOnInit();
    expect(component.underMaintainance).toBe(false);  // Mocked response value
  });
});
