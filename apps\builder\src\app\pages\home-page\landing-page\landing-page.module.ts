import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '@builder/shared/shared.module';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { NgLibModule } from '@gfk/ng-lib';
import { DxLibModule } from '@dwh/dx-lib/src';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LandingPageComponent } from './landing-page.component';

const routes: Routes = [
	{
		path: "",
		children: [
			{
				path: "",
				component: LandingPageComponent,
				canActivate: [],
				canDeactivate: []
			}
		]
	  }
  ];


@NgModule({
	declarations: [LandingPageComponent],
	imports: [
		CommonModule,
		SharedModule,
		RouterModule.forChild(routes),
		MatPaginatorModule,
		MatSortModule,
		NgLibModule,
		DxLibModule,
		FormsModule,
		ReactiveFormsModule
	],
	exports: [RouterModule],
	schemas: [
		CUSTOM_ELEMENTS_SCHEMA
	]
})
export class LandingPageModule {}
