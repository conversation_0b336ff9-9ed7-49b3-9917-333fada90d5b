<mat-form-field class="chip-list" appearance="outline">
	<mat-chip-list class="chip-list-wrapper" #chipListCountry aria-label="Countries selection">
		<mat-chip *ngFor="let chip of chips" [selectable]="true" [removable]="true" (removed)="onChipRemove(chip)">
			{{ chip.name }}
			<mat-icon matChipRemove>cancel</mat-icon>
		</mat-chip>
		<input
			[placeholder]="(!chips.length) ? placeholder : ''"
			#input
			[formControl]="inputFC"
			[matAutocomplete]="autoCountry"
			[matChipInputFor]="chipListCountry"
			[id]="id"
			[matChipInputSeparatorKeyCodes]="SEPARATOR_KEYS_CODES"
			[ngClass]="'js-filter-inp-' + inputclass"
		/>
	</mat-chip-list>
	<mat-autocomplete #autoCountry="matAutocomplete" [autoActiveFirstOption]="true" (optionSelected)="onOptionSelected($event)" (opened)="onAutocompleteOpened()">
		<mat-option *ngFor="let option of filtered" [value]="option">
			<mat-checkbox class="option-checkbox" [checked]="option.isSelected" (change)="toggleSelection(option)" (click)="$event.stopPropagation()">
				{{ option.name }}
			</mat-checkbox>
		</mat-option>
	</mat-autocomplete>
</mat-form-field>
