import { Injectable, inject } from '@angular/core';
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, throwError, timer, take } from 'rxjs';
import { catchError, retryWhen, mergeMap } from 'rxjs/operators';
import { NotificationService, NotificationVariant } from '@gfk/ng-lib';

let countRetries = 0;
const maxRetries = 3;

@Injectable()
export class GlobalHttpInterceptorService implements HttpInterceptor {

  defaultError = {
    message: 'The Ultimate Error of Life, the Universe, and Everything',
    code: 42,
  };

  private readonly notificationService: NotificationService = inject(NotificationService);

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    const modifiedRequest = req.clone({
      setHeaders: {
        'custom-countryId': '12345',
      }
    });
    console.log(modifiedRequest)
    return next.handle(modifiedRequest).pipe(
      retryWhen(errors => {
        countRetries++;
        return errors
            .pipe(
              mergeMap(error => (error.status === 401 || error.status === 403) && countRetries < maxRetries ? timer(0) : throwError(() => new HttpErrorResponse(error))),
              take(maxRetries)
            )
      }),
      catchError((reqError: HttpErrorResponse) => {
        countRetries = 0;
        const errorMessage = this.generateMessage(reqError);

        if (reqError.status === 401 || reqError.status === 403) {
          errorMessage.message = 'There is a problem with authentication. You are unauthorized. Please try again!';
        }

        this.notifyWidget((errorMessage ? errorMessage : reqError.message));
        return throwError(() => new Error(reqError.message));
      })
    );
  }

  private generateMessage(error: any) {
    const message = error?.message == null ?  error?.error : error?.message;
    const title = error?.name == null ? error.error : error?.name;
    return {title: title, message: message};
  }

  notifyWidget(errorObj): void {
    this.notificationService.errorAlert({
      title: errorObj.title,
      message: errorObj.message,
      variant: NotificationVariant.COMPACT,
    });
  }
}
