<header #TopNavHeader class="gfk-top-header nav-bg flex items-center text-xs">
  <gfk-nav landingUrl="https://home.de.vpc1445.in.gfk.com/" [externalLinks]="false" [appName]="TestApp" class="mb-0" *ngIf="hideNav && !underMaintainance">
    <div *gfkSection class="flex items-center">
      <a class="text-interactive no-underline headline" routerLinkActive="headline-active"
        [ngClass]="(currentTab === 'help-and-support') ? 'headline-active' : ''" (click)="navigateToSupportLink()">Help and support</a>
      <a class="text-interactive no-underline headline" routerLinkActive="headline-active"
        [ngClass]="(currentTab === 'settings') ? 'headline-active' : ''" (click)="tabActive('settings')" routerLink="/settings/user-role-list">Settings</a>
      <ng-container *ngIf="userDetails; else notLoggedIn">
				<gfk-user-profile-settings
          (onLogout)="signOut()" 
          [userName]="userDetails?.lastName + ', ' + userDetails?.firstName" 
          [isLoggedIn]="true">
				</gfk-user-profile-settings>
			</ng-container>
			<ng-template #notLoggedIn>
				<gfk-user-profile-settings
					[userName]="'Not logged in (Refresh)'"
					[isLoggedIn]="true"
					(onLogout)="signIn()">
				</gfk-user-profile-settings>
			</ng-template>
    </div>
  </gfk-nav>
  <gfk-nav landingUrl="https://home.de.vpc1445.in.gfk.com/" [externalLinks]="false" [appName]="TestApp" class="mb-0" *ngIf="underMaintainance">
    <div *gfkSection class="flex items-center">
      <a class="text-interactive no-underline headline" routerLinkActive="headline-active"
        [ngClass]="(currentTab === 'help-and-support') ? 'headline-active' : ''" (click)="navigateToSupportLink()">Help and support</a>
      <ng-container *ngIf="userDetails; else notLoggedIn">
				<gfk-user-profile-settings
          (onLogout)="signOut()" 
          [userName]="userDetails?.lastName + ', ' + userDetails?.firstName" 
          [isLoggedIn]="true">
				</gfk-user-profile-settings>
			</ng-container>
			<ng-template #notLoggedIn>
				<gfk-user-profile-settings
					[userName]="'Not logged in (Refresh)'"
					[isLoggedIn]="true"
					(onLogout)="signIn()">
				</gfk-user-profile-settings>
			</ng-template>
    </div>
  </gfk-nav>
  <gfk-nav landingUrl="https://home.de.vpc1445.in.gfk.com/" [externalLinks]="false" [appName]="TestApp" class="mb-0" *ngIf="!hideNav && !underMaintainance">
    <a *gfkNavItem
      [ngClass]="currentTab === '/base-projects' || currentTab === '/base-projects/list' || currentTab === '/base-projects/create' ? 'nav-tab tab-active' : 'nav-tab'"
      routerLinkActive="tab-active" (click)="tabActive('base-projects')" routerLink="/base-projects/list">Base Projects</a>
    <a *gfkNavItem
      [ngClass]="(currentTab === 'retailer-separation') ? 'nav-tab tab-active' : 'nav-tab'"
      routerLinkActive="tab-active" (click)="tabActive('retailer-separation')" routerLink="/retailer-separation/list">Retailer Separation</a>
      <a *gfkNavItem
      [ngClass]="(currentTab === 'production-projects') ? 'nav-tab tab-active' : 'nav-tab'"
      routerLinkActive="tab-active" (click)="tabActive('production-projects')">Production Projects</a>
    <a *gfkNavItem
      [ngClass]="(currentTab === 'reporting-base-projects') ? 'nav-tab tab-active' : 'nav-tab'"
      routerLinkActive="tab-active" (click)="tabActive('reporting-base-projects')">Reporting Base Projects</a>
    <a *gfkNavItem
      [ngClass]="(currentTab === 'workspaces') ? 'nav-tab tab-active' : 'nav-tab'"
      routerLinkActive="tab-active" (click)="tabActive('workspaces')">Workspaces</a>
    <div *gfkSection class="flex items-center">
      <a class="text-interactive no-underline headline" routerLinkActive="headline-active"
        [ngClass]="(currentTab === 'help-and-support') ? 'headline-active' : ''" (click)="navigateToSupportLink()">Help and support</a>
      <a class="text-interactive no-underline headline" routerLinkActive="headline-active"
        [ngClass]="(currentTab === 'settings') ? 'headline-active' : ''" (click)="tabActive('settings')" routerLink="/settings/user-role-list">Settings</a>
      <ng-container *ngIf="userDetails; else notLoggedIn">
				<gfk-user-profile-settings
          (onLogout)="signOut()" 
          [userName]="userDetails?.lastName + ', ' + userDetails?.firstName" 
          [isLoggedIn]="true">
				</gfk-user-profile-settings>
			</ng-container>
			<ng-template #notLoggedIn>
				<gfk-user-profile-settings
					[userName]="'Not logged in (Refresh)'"
					[isLoggedIn]="true"
					(onLogout)="signIn()">
				</gfk-user-profile-settings>
			</ng-template>
    </div>
  </gfk-nav>
</header>
  