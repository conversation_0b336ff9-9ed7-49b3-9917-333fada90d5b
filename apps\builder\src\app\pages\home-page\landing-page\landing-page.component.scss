@use 'sass:map' as map;
@use '../../../../styles/theme/gfk-light.palette.scss' as gfk;
@use '@gfk/style' as gfk-style;

.header-spacing {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.heading {
	border-top: 0px solid rgb(223 226 229);
	border-bottom: 1px solid rgb(223 226 229);
	padding: 10px 0px;
}

/* Remove extra left and right margins, due to padding */
.card-row {
	margin: 1.5em 0px 0px 1.5em;
  max-width: 1440px;
}

/* Clear floats after the columns */
.card-row:after {
	content: '';
	display: table;
	clear: both;
}

.card-column {
	float: left;
	width: 33%;
	padding: 8px 8px;
	box-sizing: border-box;
	display: inline-flex;
}