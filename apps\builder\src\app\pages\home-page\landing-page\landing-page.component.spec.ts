import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { LandingPageComponent } from './landing-page.component';

describe('LandingPageComponent', () => {
  const getApiServiceMock = {
    getLoggedInUserRole: jest.fn().mockReturnValue(of([]))
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [LandingPageComponent],
      providers: [
        { provide: GetApiService, useValue: getApiServiceMock }
      ],
    }).compileComponents();
  });

  it('should create', () => {
    expect(LandingPageComponent).toBeTruthy();
  });

});