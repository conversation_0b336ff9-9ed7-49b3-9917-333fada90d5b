<div class="main-container">
  <div class="message-container">
    <span><mat-icon class="red-icon">cancel</mat-icon></span>
    <span>Authentification failed!</span>
    <span>If you want to try again click <a routerLink="/home">here</a> <br> or get in touch with the support with the following error:</span>
    <mat-accordion class="error-message-container" *ngIf="errorMessage">
      <mat-expansion-panel hideToggle>
        <mat-expansion-panel-header (click)="toggleIcon()">
          <mat-panel-title>
            <mat-icon>{{iconArrowDown  ? 'keyboard_arrow_down' : 'keyboard_arrow_up' }}</mat-icon>
            {{errorMessageShort}}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <p>{{errorMessage}}</p>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>

