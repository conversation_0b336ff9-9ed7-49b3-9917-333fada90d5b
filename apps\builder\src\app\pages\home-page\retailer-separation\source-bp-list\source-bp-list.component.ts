import { ChangeDetectorRef, Component, computed, inject, OnInit, QueryList, ViewChildren } from '@angular/core';
import { HasMultiSelectTable } from '@builder/shared/components/has-multi-select-table/has-multi-select-table';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { EdsTr, EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { filter, Subscription } from 'rxjs';
import * as moment from 'moment-timezone';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { PutApiService } from '@builder/shared/services/put-api.service';
import { SignalRService } from '@builder/shared/services/signalR.service';

export interface PageChange {
	totalPages: number;
	pageIndex: number;
	pageSize: number;
	previousPageIndex: number;
}

export interface AutocompleteOption {
	value: string;
	label: string;
}

@Component({
	selector: 'dx-source-bp-list',
	templateUrl: './source-bp-list.component.html',
	styleUrls: ['./source-bp-list.component.scss'],
})

export class SourceBPListComponent extends HasMultiSelectTable<any> implements OnInit{
	
	@ViewChildren(EdsTr) _allRows!: QueryList<EdsTr>;
	allRows = computed(() => this.filterBodyRows(this._allRows.toArray()));
	@ViewChildren(EdsTr) selectedRows!: QueryList<EdsTr>;
	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);
	subscription!: Subscription;
	userRole: any;
	sourceBPData!: any;
	visibleSourceBPList: any[] = [];
	pageSize: any;
	currentPage = 1;
	readonly defaultPageSize = 10;
	readonly pageSizeOptions: number[] = [10, 25, 50, 100];
	retailerSeparationStatus: any;
	confirmationModal!: boolean;
	retailerSeparationDetails: any = [];
	hideDownload = false;
	requestID: any
	jiraID: any;
	declineMessage!: any
	declineConfirmationModal!: boolean;
	finishConfirmationModal!: boolean;
	loadTableData!: boolean;
	timezone = moment.tz.guess();
	isProdBaseUrl!: boolean;

	getId({ id }: any): number {
		return id;
	}

	constructor(
		private getApiService: GetApiService,
		protected cdRef: ChangeDetectorRef,
		private activatedRoute: ActivatedRoute,
		private postApiService: PostApiService,
		private router: Router,
		private putApiService: PutApiService,
		private signalRService: SignalRService
	){
		super(cdRef);
		this.pageSize = this.defaultPageSize;
	}

	ngOnInit(){
		this.subscription = this.getApiService.getUserRoleSubject().subscribe((userRole) => {
			this.userRole = userRole;
		});
		this.requestID = this.activatedRoute.snapshot.params.id; 
		this.getSourceBPListByRequestID(this.requestID);
		this.signalRService.message$.subscribe((notificationMessage) => {
			if(notificationMessage){
				this.getSourceBPListByRequestID(this.requestID);
			}
		});
		const hostname = window.location.hostname;
		this.isProdBaseUrl = hostname === 'builder.dwh.in.gfk.com';
	}

	getSourceBPListByRequestID(requestID: any) {	
		this.loadTableData = true;
		this.postApiService.getRetailerSeparationList({ids: [requestID]}).subscribe({
			next: (result: any) => this.handleSourceBPListResponse(result),
			error: (error) => this.handleSourceBPListError(error)
		});
	}

	handleSourceBPListResponse(result: any) {
		this.sourceBPData = [];
		this.visibleSourceBPList = [];
		this.retailerSeparationDetails = [];	
		if (result.moreRecordsAvailable) {
			const title = 'More than 1000 results exist!';
			this.notifyWidget(title, 'info');
		}	
		result.records.forEach(record => {
			this.jiraID = record.jiraId;
			this.retailerSeparationStatus = record.requestStatus;
			this.bindRSDetailsData(record.retailerSeperationRequestDetails);
			this.updateSourceBPDataForGrid(record);
		});
		this.visibleSourceBPList = this.getPageSourceBP(this.currentPage, this.pageSize);
		this.loadTableData = false;
	}

	bindRSDetailsData(details: any){
		details.forEach((item: any) => {
			const date = new Date(item.updatedWhen.toString());
			const timeZoneAbbreviation = this.getOffsetAtTime(this.timezone, item.updatedWhen);
			const cetTime = moment(date).tz(this.timezone).format('YYYY-MM-DD HH:mm');
			this.retailerSeparationDetails.push({
				updatedBy: item.updatedBy,
				updatedWhen: cetTime + ' ' + timeZoneAbbreviation,
				requestStatus: item.requestStatus
			});
		});
		this.sortRSDEtailData();
	}

	sortRSDEtailData(){
		const requestStatusOrder = {
			"Pending": 0,
			"Edited": 1,
			"Executing": 2,
			"Finished": 3
		};
		this.retailerSeparationDetails.sort((a, b) => {
			return requestStatusOrder[a.requestStatus] - requestStatusOrder[b.requestStatus];
		});
	}

	updateSourceBPDataForGrid(record: any){
		record.retailerSeperations.forEach((rs: any) => {
			this.sourceBPData.push({
				fromPeriodId: (record.fromPeriodId == 0) ? 'ALL' : record.fromPeriodId,
				toPeriodId: (record.toPeriodId == 0) ? 'ALL' : record.toPeriodId,
				extrapolation: record.extrapolation ? 'Yes' : 'No',
				resetCorrection: record.resetCorrection ? 'Yes' : 'No',
				sourceBPId: rs.sourceBPId,
				retailerBPId: (rs.retailerBPId == 0) ? '-' : rs.retailerBPId,
				isError: rs.isError ? 'ERROR' : '-',
				newSourceBaseProjectID: 1,
				retailerListID: 'ALL',
				outletListID: 'ALL',
				separationMode: this.setSeperationModeValue(record.extrapolation, record.resetCorrection)
			});
		});
		const allErrorTrue = this.sourceBPData.every(item => item.isError === 'ERROR');
		if(allErrorTrue){
			this.hideDownload = true;
		}
	}

	setSeperationModeValue(extrapolation: any, resetCorrection: any){
		let value: any;
		if(extrapolation && resetCorrection){
			value = 1;
		}
		else if(extrapolation && !resetCorrection){
			value = 2;
		}
		else if(!extrapolation && resetCorrection){
			value = 3;
		}
		else if(!extrapolation && !resetCorrection){
			value = 4;
		}
		return value;
	}

	handleSourceBPListError(error: any) {
		let title: string;
		if (error.status == 404) {
			this.sourceBPData = null;
			this.visibleSourceBPList = [];
		} 
		else if ([400, 401, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to fetch Source BP List. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	onPageChangeSourceBP(event: PageChange) {
		this.currentPage = event.pageIndex;
		this.pageSize = event.pageSize;
		this.visibleSourceBPList = this.getPageSourceBP(this.currentPage, this.pageSize);
		this.cdRef.markForCheck();
	}

	private calculatePageStart(page: number, size: number): number {
		return (page - 1) * size;
	}

	private calculatePageEnd(page: number, size: number): number {
		return page === 0 ? size : page * size;
	}

	private getPageSourceBP(page: number, size: number) {
		const start = this.calculatePageStart(page, size);
		const end = this.calculatePageEnd(page, size);
		return this.sourceBPData.slice(start, end);
	}

	transformData(inputData: any) {
		return inputData.map(item => ({
		  'Source BaseProject ID': item.sourceBPId.toString().trim(),
		  'Target BaseProject ID': item.retailerBPId.toString().trim(),
		  'from Period ID': item.fromPeriodId == 'ALL' ? item.fromPeriodId.toString().trim() : item.fromPeriodId,
		  'to Period ID': item.toPeriodId == 'ALL' ? item.toPeriodId.toString().trim() : item.toPeriodId,
		  'New Source BaseProject Type ID': item.newSourceBaseProjectID.toString().trim(),
		  'Retailer List (IDs)': item.retailerListID.toString().trim(),
		  'Outlet List (IDs)': item.outletListID.toString().trim(),
		  'Separation Mode': item.separationMode.toString().trim(),
		}));
	}

	downloadCSV(dataArray, filename) {
		// Data array ko filter karna taake sirf wo rows aayein jisme isError 'ERROR' na ho
		const filteredArray = dataArray.filter(item => item.isError !== 'ERROR');
		if (filteredArray?.length) {
			// Agar koi transformation ki zarurat ho toh
			const transformedDataArray = this.transformData(filteredArray);
	
			// CSV format mein data ko convert karna
			const csvRows: any = [];
	
			// Headers ko pehle row se le lo
			const headers = Object.keys(transformedDataArray[0]);
			csvRows.push(headers.join(',')); // Headers ko comma se join karna
	
			// Har row ko CSV format mein convert karna
			for (const row of transformedDataArray) {
				const values = headers.map(header => {
					let value = row[header];
	
					// Agar value number hai, toh usay string mein convert karna
					if (typeof value === 'number') {
						value = value.toString();  // Scientific notation se bachne ke liye string mein convert karna
					}
	
					// Agar field 'from Period ID' ya 'to Period ID' hai, toh usay string ke roop mein treat karna
					if (header === 'from Period ID' || header === 'to Period ID') {
						// Yeh ensure karega ke Excel is value ko text format mein treat kare
						value = `="${value}"`;
					}
	
					// Quotes ko escape karna aur CSV format ke liye wrap karna
					const escaped = ('' + value).replace(/"/g, '""');
					return `"${escaped}"`; // CSV format ke liye quotes mein wrap karna
				});
	
				// Formatted row ko CSV array mein add karna
				csvRows.push(values.join(',')); // Values ko comma se join karna
			}
	
			// CSV string bana lena
			const csvString = csvRows.join('\n');
	
			// Blob bana kar CSV string se
			const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
	
			// File download karne ke liye ek link banana
			const link = document.createElement('a');
			const url = URL.createObjectURL(blob);
			link.setAttribute('href', url);
			const date = new Date();
			const currentFormattedTime = moment(date).tz(this.timezone).format('YYYY-MM-DD HH.mm.ss');
			link.setAttribute('download', filename + ' ' + currentFormattedTime + '.csv');
			link.style.visibility = 'hidden';
	
			// Link ko body mein add karna aur click simulate karna taake download ho jaye
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link); // Download ke baad link ko remove karna
		}
		else {
			const title = 'Unable to download CSV';
			const message = 'Source BPs having error cannot be downloaded.';
			this.notifyWidget(title, 'error', message);
		}
	}

	openConfirmationModal() {
		this.confirmationModal = true;
	}

	confirmationModalAction(confirmDeletion?: boolean, actionType?: string): void {
		if (!confirmDeletion) {
			this.confirmationModal = false;
		}
		else{
			this.loadTableData = true;
			const payload = [this.requestID];
			this.confirmationModal = false;
			this.postApiService.executeRetailerSeparation({retailerSeperationRequestIds: payload}).subscribe({
				next: () => this.handleExecuteResponse(),
				error: (error) => this.handleExecuteError(error),
				complete: () => this.getSourceBPListByRequestID(this.requestID)
			});
		}
	}

	handleExecuteResponse(){
		const title = 'Retailer Separation Started';
		const message = "The request is being processed and may take a few minutes. A confirmation notification will be sent once it's complete.";
		this.notifyWidget(title, 'info', message);
		this.loadTableData = false;
	}

	handleExecuteError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to execute Retailer Separation. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	setQueryParams(sourceBPID: any) {
		if (this.sourceBPData.length > 1) {
		  	return { editall: false, sourceBPID: sourceBPID }; 
		} else {
			return { edit: true, sourceBPID: sourceBPID };
		}
	}

	openDeclineConfirmationModal() {
		this.declineConfirmationModal = true;
		this.declineMessage = '';
	}

	declineConfirmationModalAction(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.declineConfirmationModal = false;
		}
		else{
			this.loadTableData = true;
			this.declineConfirmationModal = false;
			this.putApiService.updateRetailerSeparationStatus(
				[this.requestID],
				5,
				this.declineMessage
			).subscribe({
				next: () => this.handleStatusChangeResponse('decline'),
				error: (error) => this.handleStatusChangeError(error),
				complete: () => this.handleStatusChangeComplete('decline')
			});
		}
	}

	openFinishConfirmationModal() {
		this.finishConfirmationModal = true;
	}

	finishConfirmationModalAction(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.finishConfirmationModal = false;
		}
		else{
			this.loadTableData = true;
			this.finishConfirmationModal = false;
			this.putApiService.updateRetailerSeparationStatus(
				[this.requestID],
				3,
				''
			).subscribe({
				next: () => this.handleStatusChangeResponse(),
				error: (error) => this.handleStatusChangeError(error),
				complete: () => this.handleStatusChangeComplete()
			});
		}
	}

	handleStatusChangeResponse(statusType?: any){
		this.clearSelection();
		const title = statusType == 'decline' ? 'I/R Separation Request Deleted' : 'I/R Request Status Updated';
		this.notifyWidget(title, 'success');
		this.loadTableData = false;
	}

	handleStatusChangeError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to change I/R request status. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	handleStatusChangeComplete(statusType?: any){
		if(statusType == 'decline'){
			this.router.navigate(['/retailer-separation/list']);
		}
		else{
			this.getSourceBPListByRequestID(this.requestID)
		}
		this.loadTableData = false;
	}

	getOffsetAtTime(timezone, date) {
		const zone = moment.tz.zone(timezone);
  
		if (zone) {
			const timestamp = moment(date).valueOf(); // Convert date to timestamp
			const abbrs = zone.abbrs; // Get the list of abbreviations
			const untils = zone.untils; // Get the list of timestamp changes (for daylight saving time changes)
			
			// Find the correct abbreviation based on the timestamp
			for (let i = 0; i < untils.length; i++) {
			if (timestamp < untils[i]) {
				return abbrs[i]; // Return abbreviation if timestamp is before the DST change
			}
			}
			
			// If no matching change is found (for times after the last DST change), use the last abbreviation
			return abbrs[abbrs.length - 1]; // Return the last abbreviation
		} else {
			return null; // Return null if the timezone is not found
		}
	}

	notifyWidget(title: string, notificationType: string, message?: string): void {
		if(notificationType == 'info'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if(notificationType == 'warning'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.WARNING,
				message: message || '',
				actionLabel: 'Note: CSV will be available for download once request is completed.'
			});
		}
		else if(notificationType == 'error'){
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else{
			this.notificationService.showNotification(title, {
				message: message || '',
				duration : 15000,
				variant: NotificationVariant.SUCCESS,
			});
		}
	}
}