import { Component, inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { RadioLabelsModel } from '@gfk/ng-lib/lib/components/radio-buttons/models/radio-buttons.interface';
import { filter, map, Observable, Subscription, takeUntil } from 'rxjs';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PutApiService } from '@builder/shared/services/put-api.service';
import { DeleteApiService } from '@builder/shared/services/delete-api.service';
import { SignalRService } from '@builder/shared/services/signalR.service';

export interface AutocompleteOption {
	value: string;
	label: string;
}

@Component({
	selector: 'dx-retailer-separation-add',
	templateUrl: './retailer-separation-add.component.html',
	styleUrls: ['./retailer-separation-add.component.scss'],
})

export class RetailerSeparationAddComponent implements OnInit {

	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);
	subscription!: Subscription;
	userRole: any;
	retailerSeparationForm!: FormGroup;
	extrapolationFactorLabelGroup: RadioLabelsModel = {
		label: 'Extrapolation Factor = 1 *',
	};
	resetCorrectionsLabelGroup: RadioLabelsModel = {
		label: 'Reset Corrections *',
	};
	extrapolationOptions = [
		{ title: "Yes", inputClass: '', name: 'radioGroup', value: true, hasError: false, checked: true, disabled: false },
		{ title: "No", inputClass: '', name: 'radioGroup', value: false, hasError: false, checked: false, disabled: false }
	];
	resetCorrectionsOptions = [
		{ title: "Yes", inputClass: '', name: 'radioGroup', value: true, hasError: false, checked: true, disabled: false },
		{ title: "No", inputClass: '', name: 'radioGroup', value: false, hasError: false, checked: false, disabled: false }
	];
	sourceBPList!: any;
	fromPeriodCheckBoxState = true;
	toPeriodCheckBoxState = true;
	showSourceBPError = false;
	requestID!: any;
	rsRequestData: any;
	markErrorConfirmationModal!: boolean;
	editAllParam!: any;
	editParam!: any;
	deleteConfirmationModal!: boolean;
	disablePeriodCheckbox!: boolean;
	selectedSourceBPID: any;
	deleteMessage: any;
	errorMessage: any;
	selectedSourceBPLabels: Array<string> = [];
	IRValidationModal = false;
	bpIds = '';
	sourceBPList$!: Observable<AutocompleteOption[]>;
	loadTableData!: boolean;
	isProdBaseUrl!: boolean;

	constructor(
		private formBuilder: FormBuilder,
		private postApiService: PostApiService,
		private router: Router,
		private activatedRoute: ActivatedRoute,
		private getApiService: GetApiService,
		private putApiService: PutApiService,
		private deleteApiService: DeleteApiService,
		private signalRService: SignalRService
	) { }

  ngOnInit() {
    this.subscription = this.getApiService.getUserRoleSubject().subscribe((userRole) => {
        this.userRole = userRole;
    });
    this.requestID = this.activatedRoute.snapshot.params.id;
    const params = this.activatedRoute.snapshot.queryParams;
    if (params) {
        if (params['editall'] || params['editAll']) {
            this.editAllParam = params['editall'] || params['editAll'];
        }
        if (params['edit']) {
            this.editParam = params['edit'];
        }
        if (params['sourceBPID']) {
            this.selectedSourceBPID = params['sourceBPID'];
        }
    }

    if (this.requestID) {
        this.loadBRetailerSeparationDataByID(this.requestID);
    } else {
        this.getSourceBPList();
    }

    this.initializeRetailerSeparationForm();
    this.initializeSourceBPValueChange();
	this.signalRService.message$.subscribe((notificationMessage) => {
		if(notificationMessage){
			if(this.requestID){
				this.loadBRetailerSeparationDataByID(this.requestID);
			}
		}
	});

	const hostname = window.location.hostname;
	this.isProdBaseUrl = hostname === 'builder.dwh.in.gfk.com';
}


  getSourceBPList() {
    this.sourceBPList$ = this.getApiService.getAllBPList(3)
    .pipe(
        map((sourceBPList: any) => sourceBPList.records.map((sourceBP: any) => (
            {
                label: `${sourceBP.name} - (${sourceBP.id})`,
                value: sourceBP.id.toString()
            } as AutocompleteOption
        )))
    );

    this.sourceBPList$.subscribe({
        next: (result: any) => {
            this.sourceBPList = result;
            if (this.selectedSourceBPID) {
                const matchedBP = this.sourceBPList.find((bp: any) => bp.value === this.selectedSourceBPID);
                if (matchedBP) {
                    this.retailerSeparationForm.get('sourceBPId')?.setValue([matchedBP.value]);
                }
            }
        }
    });
}


	initializeRetailerSeparationForm() {
		this.retailerSeparationForm = this.formBuilder.group({
			sourceBPId: ['', { nonNullable: true }],
			fromPeriodId: [null],
			toPeriodId: [null],
			resetCorrection: true,
			extrapolation: true
		});
		this.retailerSeparationForm.get('fromPeriodId')?.disable();
		this.retailerSeparationForm.get('toPeriodId')?.disable();
	}

	initializeSourceBPValueChange() {
		this.retailerSeparationForm.get('sourceBPId')?.valueChanges.subscribe((value: any) => {
			if (value.length > 1 && value.length <= 20) {
				this.showSourceBPError = false;
				this.fromPeriodCheckBoxState = true;
				this.toPeriodCheckBoxState = true;
				this.retailerSeparationForm.get('fromPeriodId')?.setValue(null);
				this.retailerSeparationForm.get('fromPeriodId')?.disable();
				this.retailerSeparationForm.get('toPeriodId')?.setValue(null);
				this.retailerSeparationForm.get('toPeriodId')?.disable();
			}
			else if (value.length > 20) {
				this.showSourceBPError = true;
			}
		});
	}

	saveRetailerSeparation() {
		this.loadTableData = true;
		if (this.requestID) {
			this.updateRetailerSeparation();
		}
		else {
			this.createRetailerSeparation();
		}
	}

	convertStringToBool(input: any) {
		if (input == 'true' || input == true) {
			return true;
		}
		else {
			return false;
		}
	}

	checkNullPeriodValue(input: any) {
		if (!input) {
			return 0;
		}
		else {
			return input;
		}
	}

	handleSourceBPIDValue(input: any) {
		const convertedArray = input.map(id => ({ sourceBPId: id }));
		return convertedArray;
	}

	disableInputField(event: any, fieldName: string, checkBoxName: string) {
		if (event.detail) {
			this.retailerSeparationForm.get(fieldName)?.disable();
		}
		else {
			this.retailerSeparationForm.get(fieldName)?.enable();
		}
		if (checkBoxName == 'allFromPeriod') {
			this.fromPeriodCheckBoxState = event.detail;
		}
		else {
			this.toPeriodCheckBoxState = event.detail;
		}
		this.retailerSeparationForm.get(fieldName)?.setValue(null);
	}

	createRetailerSeparation() {
		this.postApiService.addRetailerSeparation(
			this.checkNullPeriodValue(this.retailerSeparationForm.get('fromPeriodId')?.value),
			this.checkNullPeriodValue(this.retailerSeparationForm.get('toPeriodId')?.value),
			this.convertStringToBool(this.retailerSeparationForm.get('resetCorrection')?.value),
			this.convertStringToBool(this.retailerSeparationForm.get('extrapolation')?.value),
			this.handleSourceBPIDValue(this.retailerSeparationForm.get('sourceBPId')?.value),
		).subscribe({
			next: (response) => this.handleRSCreationSuccess(response),
			error: (error) => this.handleRSCreationError(error),
			complete: () => this.handleRSCreationComplete(),
		});
	}

	handleRSCreationSuccess(response: any) {
		const title = 'I/R Separation Requested';
		const message = 'ID : ' + response.jiraId;
		this.notifyWidget(title, 'success', message, response.jiraId);
		this.loadTableData = false;
	}

	handleRSCreationError(error: any) {
		let title: string;
		if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 502 || error.status == 503 || error.status == 504) {
			title = 'Error: ' + error.status + '. Retailer Separation creation failed. Please contact administrator.';
			if(error.error.message.includes('QC Status')){
				const extractedIds = this.extractIdsFromErrorMessage(error.error.message);
				this.bpIds=extractedIds;
				this.openIRValidationModal();
			}
			else{
				this.notifyWidget(title, 'error');
			}
		}
		this.loadTableData = false;
	}

	handleRSCreationComplete() {
		this.router.navigate(['/retailer-separation/list']);
	}

	loadBRetailerSeparationDataByID(requestID: any) {
		this.loadTableData = true;
		this.getApiService.getRetailerSeperationRequestByID(requestID)
			.subscribe({
				next: (response) => this.handleGetRSRequestSuccess(response),
				error: (error) => this.handleGetRSRequestError(error)
			})
	}

	handleGetRSRequestSuccess(response: any) {
		this.rsRequestData = response;
		this.rsRequestData.requestStatus = this.setRequestStatus(response.requestStatusId);
		if(this.selectedSourceBPID){
			this.rsRequestData.isError = this.rsRequestData.retailerSeperations.find(separation => separation.sourceBPId == this.selectedSourceBPID).isError;
		}
		this.bindRSDataForEdit(response);
		this.initializeSourceBPValueChange();
		this.disableRequestForm();
		this.loadTableData = false;
	}

	setRequestStatus(statusId: any) {
		const statusList = [
			{ label: 'Pending', value: 1 },
			{ label: 'Executing', value: 2 },
			{ label: 'Finished', value: 3 },
			{ label: 'Error', value: '6' },
			{ label: 'CSV Available', value: 6 }
		];
		const status = statusList.find(status => status.value === statusId);
		return status?.label;
	}

	disableRequestForm() {
		setTimeout(() => {
			if (!this.userRole || this.userRole.name !== 'Master' || this.editAllParam == 'false' || this.rsRequestData.requestStatus == 'Executing' || this.rsRequestData.requestStatus == 'Finished') {
				this.retailerSeparationForm.disable();
				this.disablePeriodCheckbox = true;
			}
		}, 500);
	}

	bindRSDataForEdit(rsData: any) {
		this.patchRSForm(rsData);
		this.setInitialCheckedOptions(rsData);
		this.setCheckBoxStateOnEdit(rsData);
	}

	patchRSForm(rsData: any) {
		this.retailerSeparationForm = this.formBuilder.group({
			sourceBPId: [],
			fromPeriodId: rsData.fromPeriodId,
			toPeriodId: rsData.toPeriodId,
			resetCorrection: rsData.resetCorrection.toString(),
			extrapolation: rsData.extrapolation.toString()
		});
		this.retailerSeparationForm?.get('sourceBPId')?.disable();
		this.bindSourceBPID(rsData.retailerSeperations, this.selectedSourceBPID)
	}

	bindSourceBPID(data: any, sourceBPID?: any) {
		this.selectedSourceBPLabels = [];
		let selectedSourceBP: any;
		if (sourceBPID) {
			this.selectedSourceBPLabels = data.filter(item => item.sourceBPId == sourceBPID).map(item => item.sourceBPName);
			selectedSourceBP = sourceBPID.toString();
			this.retailerSeparationForm.get('sourceBPId')?.setValue([selectedSourceBP]);
		}
		else {
			this.selectedSourceBPLabels = data.map(item => item.sourceBPName);
			selectedSourceBP = data.map(item => item.sourceBPId.toString());
			this.retailerSeparationForm.get('sourceBPId')?.setValue(selectedSourceBP);
		}
		return selectedSourceBP;
	}

	setInitialCheckedOptions(rsData: any) {
		setTimeout(() => {
			this.extrapolationOptions.forEach(item => {
				item.checked = item.value == rsData.extrapolation;
			});
			this.resetCorrectionsOptions.forEach(item => {
				item.checked = item.value == rsData.resetCorrection;
			});
		}, 500);
	}

	setCheckBoxStateOnEdit(data: any) {
		this.fromPeriodCheckBoxState = data.fromPeriodId == 0 ? true : false;
		this.toPeriodCheckBoxState = data.toPeriodId == 0 ? true : false;
		if (this.fromPeriodCheckBoxState || data.retailerSeperations.length > 1) {
			this.retailerSeparationForm.get('fromPeriodId')?.disable();
			this.retailerSeparationForm.get('fromPeriodId')?.setValue(null);
		}
		if (this.toPeriodCheckBoxState || data.retailerSeperations.length > 1) {
			this.retailerSeparationForm.get('toPeriodId')?.disable();
			this.retailerSeparationForm.get('toPeriodId')?.setValue(null);
		}
	}

	handleGetRSRequestError(error: any) {
		let title: string;
		if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 502 || error.status == 503 || error.status == 504) {
			title = 'Error: ' + error.status + '. Unable to fetch Retailer Separation data. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}


	openDeleteConfirmationModal() {
		this.deleteConfirmationModal = true;
		this.deleteMessage = '';
	}

	deleteRetailerSeparationRequest(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.deleteConfirmationModal = false;
		}
		else {
			this.loadTableData = true;
			this.deleteConfirmationModal = false;
			const selectedSourceBP = this.rsRequestData.retailerSeperations.find(item => item.sourceBPId == this.selectedSourceBPID);
			const payload = {
				retailerSeperationIds: [selectedSourceBP.id],
				message: this.deleteMessage
			}
			this.deleteApiService.deleteRetailerSeparationRequest(payload).subscribe({
				next: (data) => this.handleDeleteResponse(data),
				error: (error) => this.handleDeleteError(error),
				complete: () => this.router.navigate(['/retailer-separation/list/' + this.requestID])
			});
		}
	}

	handleDeleteResponse(data: any) {
		const title = 'BP# '+data[0].sourceBpId+' removed from I/R Request';
		this.notifyWidget(title, 'success');
		this.loadTableData = false;
	}

	handleDeleteError(error: any) {
		let title: string;
		if ([400, 401, 404, 500, 502, 503, 504].includes(error.status)) {
			title = 'Error: ' + error.status + '. Unable to delete Retailer Separation. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	openMarkErrorConfirmationModal() {
		this.markErrorConfirmationModal = true;
		this.errorMessage = '';
	}

	markErrorConfirmationModalAction(confirmDeletion?: boolean): void {
		if (!confirmDeletion) {
			this.markErrorConfirmationModal = false;
		}
		else {
			this.loadTableData = true;
			this.markErrorConfirmationModal = false;
			this.putApiService.updateRetailerSeparation(
				this.requestID,
				-1,
				-1,
				this.convertStringToBool(this.retailerSeparationForm.get('resetCorrection')?.value),
				this.convertStringToBool(this.retailerSeparationForm.get('extrapolation')?.value),
				this.changeRSErrorStatus(this.rsRequestData.retailerSeperations),
			).subscribe({
				next: () => this.handleMarkErrorSuccess(),
				error: (error) => this.handleMarkErrorError(error),
				complete: () => this.handleRSUpdateComplete()
			});
		}
	}

	changeRSErrorStatus(data: any) {
		const selectedSourceBP = data.filter(item => item.sourceBPId == this.selectedSourceBPID);
		selectedSourceBP.forEach((item: any) => {
			item.isError = !item.isError;
		});
		return selectedSourceBP.map(item => ({
			sourceBPId: item.sourceBPId,
			isError: item.isError,
			errorDetails: this.errorMessage ? this.errorMessage : ''
		}));
	}

	handleMarkErrorSuccess() {
		const title = 'I/R Request Updated';
		this.notifyWidget(title, 'success');
		this.loadTableData = false;
	}

	handleMarkErrorError(error: any) {
		let title: string;
		if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 502 || error.status == 503 || error.status == 504) {
			title = 'Error: ' + error.status + '. Unable to change error status. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	updateRetailerSeparation() {
		this.putApiService.updateRetailerSeparation(
			this.requestID,
			this.checkNullPeriodValue(this.retailerSeparationForm.get('fromPeriodId')?.value),
			this.checkNullPeriodValue(this.retailerSeparationForm.get('toPeriodId')?.value),
			this.convertStringToBool(this.retailerSeparationForm.get('resetCorrection')?.value),
			this.convertStringToBool(this.retailerSeparationForm.get('extrapolation')?.value),
			this.setSourceBPIDValue(this.rsRequestData.retailerSeperations),
		).subscribe({
			next: () => this.handleRSUpdateSuccess(),
			error: (error) => this.handleRSUpdateError(error),
			complete: () => this.handleRSUpdateComplete(),
		});
	}

	setSourceBPIDValue(data: any) {
		return data.map(item => ({
			sourceBPId: item.sourceBPId,
			isError: item.isError,
			errorDetails: ''
		}));
	}

	handleRSUpdateSuccess() {
		const title = 'I/R Request Updated';
		this.notifyWidget(title, 'success');
		this.loadTableData = false;
	}

	handleRSUpdateError(error: any) {
		let title: string;
		if (error.status == 400 || error.status == 401 || error.status == 404 || error.status == 502 || error.status == 503 || error.status == 504) {
			title = 'Error: ' + error.status + '. Retailer Separation update failed. Please contact administrator.';
			this.notifyWidget(title, 'error');
		}
		this.loadTableData = false;
	}

	handleRSUpdateComplete() {
		this.router.navigate(['/retailer-separation/list/' + this.rsRequestData?.id]);
	}

	notifyWidget(title: string, notificationType: string, message?: string, link?: string): void {
		if (notificationType == 'info') {
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if (notificationType == 'error') {
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR
			});
		}
		else {
			const jiraBaseUrl = this.isProdBaseUrl ? 'https://adlm.nielseniq.com' : 'https://adlm-tst.nielseniq.com';
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.SUCCESS,
				action: () => window.open(`${jiraBaseUrl}/jira/browse/${link}`, '_blank'),
				actionLabel: 'Click here to open this ticket on JIRA'
			});
		}
	}

	openIRValidationModal() {
		this.IRValidationModal = true;
	}

	closeIRValidationModal(): void {
		this.IRValidationModal = false;
	}

	extractIdsFromErrorMessage(errorMessage: string): string {
		const regex = /\d+/g; // Match all numeric sequences
		const ids = errorMessage.match(regex); // Extract matches
		return ids ? ids.join(',') : ''; // Join matches with a comma or return an empty string if none found
	}
}
