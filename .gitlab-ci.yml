include:
    - project: 'dp/de/shared/pipeline/generic-pipelines'
      ref: 'semantic_ver_testing'
      file: 'node/native/_pipeline.yml'
# addding comment to test semantic versiong process
variables:
    UI_NAME: 'builder-ui'
    BFF_NAME: 'builder-bff'
    CONTAINER_REGISTRY: 'nexus.gfk.com:8471'
    SONAR_PRODUCT_NAME: Dwh.Builder.UI
    SONAR_PROJECT_KEY: Dwh.Builder.UI
    GITLAB_CONTAINER_REGISTRY: "$CI_REGISTRY/dp/de/products/builder/builder-ui/${APP_NAME}:${CI_GFK_SEMVER}"
