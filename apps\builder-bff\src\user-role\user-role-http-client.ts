import axios from 'axios';
import { injectable } from 'inversify';
import { log } from '@gfk/bff/src/lib/logger';
import { ServiceNames, UrmOptions, mainContainer } from '@gfk/bff';
import { UserRoleClient } from './user-role-client';
import { Config } from '../config.type';
import { UserRole } from './models';
import { AuthClientCredentialsFlow } from '@gfk/bff/src/lib/auth/client-credentials-flow/auth-client-credentials-flow';

@injectable()
class UserRoleHttpClient implements UserRoleClient {
  private options: UrmOptions;
  private authClient:AuthClientCredentialsFlow;

  constructor() {
    this.options = mainContainer.get<Config>(ServiceNames.Config).userRoleManagement;
    this.authClient = mainContainer.get<AuthClientCredentialsFlow>(ServiceNames.AuthClientCredentialsFlow);
  }

  async getUserRole(oid: string): Promise<UserRole[]> {
    try {
      log.debug({ metaData: { class: 'UserRoleHttpClient', method: 'getUserRole' }, message: 'started' });
      const accessToken = await this.authClient.getToken(this.options.baseUri.substring(8,100),false);

      const options = {
        headers: {
          oid,
          Authorization: `Bearer ${accessToken}`
        },
      };
      const uri = `${this.options.baseUri}${this.options.roleEndpoint}`;
      const response = await axios.get<UserRole[]>(uri, options);

      log.debug({ metaData: { class: 'UserRoleHttpClient', method: 'getUserRole' }, message: response.data });

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        // console.error('Requested resource not found:', error);
        // Handle the 404 error here, such as displaying a user-friendly message or logging it
        // Do not rethrow the error to prevent the application from stopping
        return []; // Return an empty array or any default value
      } else {
        // console.error('Error fetching user role:', error);
        // You can choose to rethrow the error or handle it differently if needed
        throw error;
      }
    }
  }
}

export default UserRoleHttpClient;
