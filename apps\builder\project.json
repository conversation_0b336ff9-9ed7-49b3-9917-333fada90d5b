{"name": "builder", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/builder/src", "prefix": "lm", "tags": [], "implicitDependencies": ["dx-lib"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"allowedCommonJsDependencies": ["opentracing/lib/tracer", "opentracing/lib/span", "opentracing/lib/constants", "error-stack-parser", "lodash", "moment", "classnames"], "outputPath": "dist/apps/builder", "index": "apps/builder/src/index.html", "main": "apps/builder/src/main.ts", "polyfills": "apps/builder/src/polyfills.ts", "tsConfig": "apps/builder/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["apps/builder/src/favicon.ico", "apps/builder/src/assets", "apps/builder/src/unsupported_browser.html"], "styles": ["apps/builder/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "apps/builder/src/environments/environment.ts", "with": "apps/builder/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "apps/builder/src/proxy.conf.json"}, "configurations": {"production": {"buildTarget": "builder:build:production"}, "development": {"buildTarget": "builder:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "builder:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/builder"], "options": {"jestConfig": "apps/builder/jest.config.ts"}}}}