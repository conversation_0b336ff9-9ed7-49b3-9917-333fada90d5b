import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { StorageService } from './storage.service';

@Injectable({
	providedIn: 'root',
})
export class BannerService {
	private readonly DX_BANNER_KEY = 'dx-alpha-banner--hidden';
	private readonly storage = inject(StorageService);

	readonly hideBanner$ = new BehaviorSubject<boolean>(this.storage.get<boolean>(this.DX_BANNER_KEY) ?? false);

	get isVisible$(): Observable<boolean> {
		return this.hideBanner$.asObservable().pipe(map((v) => !v));
	}

	showBanner() {
		this.storage.remove(this.DX_BANNER_KEY);
		this.hideBanner$.next(false);
	}

	hideBanner() {
		this.hideBanner$.next(true);
		this.storage.set(this.DX_BANNER_KEY, true);
	}
}
