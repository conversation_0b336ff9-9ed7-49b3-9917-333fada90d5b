import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { TabMessage } from '../../interfaces/tab-message';
import { TabCommunicationService } from '../tab-communication.service';

const AuthenticationConfig = {
	CHANNEL_NAME: 'AUTHENTICATION',
	MESSAGE_TYPE: 'AUTHENTICATION_RESPONSE',
};

@Injectable()
export class AuthenticationTabCommunicationService extends TabCommunicationService<boolean> {
	override CHANNEL_NAME = AuthenticationConfig.CHANNEL_NAME;
	override MESSAGE_TYPE = AuthenticationConfig.MESSAGE_TYPE;

	get onAuthMessage(): Observable<boolean> {
		return this.message$.pipe(map((msg: TabMessage<boolean>) => msg.payload));
	}

	constructor() {
		super();
		super.initialize();
	}

	publish(message: boolean): void {
		this.postMessageToTab(message);
		this.postMessageToSelf(message);
	}

	private postMessageToTab(message: boolean): void {
		super.postMessage(message);
	}
	private postMessageToSelf(message: boolean): void {
		const waitTimeBeforeClosing = 500;
		setTimeout(() => this.message$.next({ type: AuthenticationConfig.MESSAGE_TYPE, payload: message }), waitTimeBeforeClosing);
	}
}
