import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FooterComponent } from './footer.component';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { SwUpdate } from '@angular/service-worker';
import { BehaviorSubject } from 'rxjs';

describe('FooterComponent', () => {
  let component: FooterComponent;
  let fixture: ComponentFixture<FooterComponent>;
  let httpTestingController: HttpTestingController;

  const mockSwUpdate = {
    isEnabled: true,
    available: new BehaviorSubject<any>(null),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      declarations: [FooterComponent],
      providers: [{ provide: SwUpdate, useValue: mockSwUpdate }],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FooterComponent);
    component = fixture.componentInstance;
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpTestingController.verify();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should fetch the version from assets/version.txt on init', () => {
    const mockVersion = '22.13.2';

    // Trigger ngOnInit
    fixture.detectChanges();

    const req = httpTestingController.expectOne((request) =>
      request.url.startsWith('assets/version.txt')
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockVersion);

    expect(component.version).toBe(mockVersion.trim());
  });


});
