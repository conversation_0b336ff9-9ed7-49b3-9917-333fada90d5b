@use 'sass:map' as map;
@use '../../../../../styles/theme/gfk-light.palette.scss' as gfk;
@use '@gfk/style' as gfk-style;

.header-spacing {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.heading {
	border-top: 1px solid rgb(223 226 229);
    border-bottom: 1px solid rgb(223 226 229);
    padding: 10px 0px
}

.chip {
    border: 1px solid #bbc0c9;
    padding: 5px 15px;
    border-radius: 9999px;
}

.chip-size {
    font-size: 13px;
}

.pending-state {
    background-color: #EEE78E;
    color: black;
}

.executing-state {
    background-color: #E6735C;
    color: white;
}

.finished-state {
    background-color: #66A586;
    color: white;
}

.edited-state {
    background-color: #D0D2C1;
    color: black;
}

.csv-available-state {
    background-color: #8CB9BD;
    color: white;
}

.error-text { 
    color: red;
}

.margin-0 {
    margin: 0px !important;
}

.rs-id-style {
	color: black;
}
 
.rs-id-style:hover {
	color: gfk-style.$brand;
}

.selected-retailer-separation-footer {	
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #ebecf0;
	padding: 20px 20px;
	position: fixed;
	width: 100%;
	bottom: 0px;
	z-index: 2;
	.m-0 {
		margin: 0px !important;
	}
	.ml-3 {
		margin-left: 12px !important;
	}
}

.transparent-background-decline-btn {
    background-color: #ffffff !important;
    color: #bc1c0e !important;
    padding: 6px 10px !important;
    border: 2px solid #bc1c0e !important;
    border-radius: 5px !important;
    font-weight: 700 !important;
    .stroke-brand {
        stroke: #bc1c0e;
    }
    .fill-brand {
        fill: #bc1c0e;
    }
}

.textarea-style {
    border-radius: 5px;
    resize: none;
    border: 1px solid #bbc0c9;
    padding: 5px;
}