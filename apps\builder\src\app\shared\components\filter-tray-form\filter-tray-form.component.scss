@use 'sass:map' as map;
@use '../../../../styles/theme/gfk-light.palette.scss' as gfk;
@use '@gfk/style' as gfk-style;


.jira-ticket-input {
    width: 100%;
    padding: 8px;
    border-radius: 3px;
    border: 1px solid #bbc0c9;
}

.filter-label {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem; /* Same as text-md */
  font-weight: 600; /* Same as font-bold */
  color: #4d4b4a; /* Orange color, same as Apply button */
  height: 1.5rem; /* Same as h-6 */
}

.filter-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #ea580c; /* Ensures orange color on modern browsers */
  cursor: pointer;
}

