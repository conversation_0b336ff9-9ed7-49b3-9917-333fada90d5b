@use 'sass:map' as map;
@use 'node_modules/@angular/material' as mat;
// Core styles that can be used to apply material design treatments to any element.
@use 'node_modules/@angular/material/core/typography/typography' as mat-typography;
@use 'node_modules/@angular/cdk/overlay' as mat-overlay;
@use 'node_modules/@angular/cdk/a11y' as mat-a11y;
@use 'node_modules/@angular/cdk/text-field' as mat-text-field;

@use 'styles/theme/gfk.typography' as gfk;
@use 'styles/theme/material-gfk.typography' as mat-gfk;


@use "@gfk/style" as gfk-style;
//@import '~@angular/cdk/overlay-prebuilt.css';
@import '@gfk/style/normalize';
@import '@gfk/style/global';

//==================================================/DO NOT USE=========================================================
// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
// Or Use the individual @includes per component
//@include mat.core(mat-gfk.$typography);

// Override typography for all Angular Material, including mat-base-typography and all components.
//@include mat.all-component-typographies(mat-gfk.$typography);
//===================================================DO NOT USE/========================================================

//=================================================/USE INSTEAD=========================================================
// Override typography for a specific Angular Material components.
@include mat-typography.typography-hierarchy(mat-gfk.$typography);
@include mat.ripple();
@include mat-a11y.a11y-visually-hidden();
@include mat-overlay.overlay();
@include mat-text-field.text-field-autosize();
@include mat-text-field.text-field-autofill();

// @include mat.badge-typography(gfk.$typography);
@include mat.autocomplete-typography(gfk.$typography);
@include mat.bottom-sheet-typography(gfk.$typography);
@include mat.button-typography(gfk.$typography);
@include mat.button-toggle-typography(gfk.$typography);
// @include mat.card-typography(gfk.$typography);
// @include mat.checkbox-typography(gfk.$typography);
@include mat.chips-typography(gfk.$typography);
@include mat.divider-typography(gfk.$typography);
@include mat.table-typography(gfk.$typography);
@include mat.datepicker-typography(gfk.$typography);
@include mat.dialog-typography(gfk.$typography);
@include mat.expansion-typography(gfk.$typography);
@include mat.form-field-typography(gfk.$typography);
@include mat.grid-list-typography(gfk.$typography);
@include mat.icon-typography(gfk.$typography);
@include mat.input-typography(gfk.$typography);
@include mat.menu-typography(gfk.$typography);
@include mat.paginator-typography(gfk.$typography);
// @include mat.progress-bar-typography(gfk.$typography);
// @include mat.progress-spinner-typography(gfk.$typography);
// @include mat.radio-typography(gfk.$typography);
@include mat.select-typography(gfk.$typography);
@include mat.sidenav-typography(gfk.$typography);
// @include mat.slide-toggle-typography(gfk.$typography);
// @include mat.slider-typography(gfk.$typography);
// @include mat.stepper-typography(gfk.$typography);
// @include mat.sort-typography(gfk.$typography);
@include mat.tabs-typography(gfk.$typography);
@include mat.toolbar-typography(gfk.$typography);
// @include mat.tooltip-typography(gfk.$typography);
@include mat.list-typography(gfk.$typography);
@include mat.option-typography(gfk.$typography);
// @include mat.optgroup-typography(gfk.$typography);
@include mat.snack-bar-typography(gfk.$typography);
// @include mat.tree-typography(gfk.$typography);
//=================================================USE INSTEAD/=========================================================

//=================================================THEME FILES/=========================================================
@import "styles/theme/gfk-light.theme";
//=================================================/THEME FILES=========================================================

//=================================================/CUSTOM GLOBAL CSS===================================================
/* You can add global styles to this file, and also import other style files */
@import 'assets/fonts/font';
@import 'assets/fonts/icons/icons-font';

//html, body { height: 100%; }

.gfk-typography {
  @include gfk.typography-base(gfk.$typography);
}

html,
body {
  @extend %font-body;
  height: 100vh;
  background-color: gfk-style.$grey-100;
}

// we use fake button in app.component instead
#atlwdg-trigger {
  display: none;
}

.progress-spinner {
  display: flex;
  width: 49px;
  justify-content: flex-end;
  align-items: center;
}

.table-grid {
  padding-top: 5px;
}

.tab-container {
  overflow: auto;
}

.columnHeader {
  background-color: #6f7582;
  color: white;
  height: 48px !important;
  font-weight: 800;
}

.each-slice-top-headbar {
  padding-top: 50px;
  display: flex;
  justify-content: space-between;
  padding-left: 23px;
}

//=================================================CUSTOM GLOBAL CSS/===================================================
.sub-nav .page-name h1 {
  margin-bottom: 0 !important;
}

.refresh-button-right {
  float: right;
  padding: 1px 20px;
  margin-top: 5px !important;
}

.clear-all-filters {
  color: gfk-style.$brand;
  font-weight: gfk-style.$font-weight-heavy;
  cursor: pointer;
  display: flex;
  align-items: flex-end;
}

.filter-options {
  height: 45px;
  display: flex;
  justify-content: space-between;
}

.min-height-quickdialog {
  min-height: 120px !important;
}

.gfk-secondary-header {
  .gfk-nav {
    &.sub-nav {
      div {
        flex-direction: column;
        align-items: flex-start;
        padding: 0px 12px;
      }
      .nav-tab {
        line-height: 2.5rem;
        padding: 8px 0;
        border-bottom: 0 none;
        color: #9499a3;
      }
      .page-name h3 {
        font-size: 1.5rem;
        color: #5c626e;
        font-weight: 400;
      }
    }
  }
}

.gfk-nav {
  nav {
    .logo-container {
      padding: 0px 0px;
      display: flex;
    }
  }
  .nav-tab {
    margin: 0px 10px !important
  }
}

.gfk-top-header {
  .gfk-nav {
    nav {
      ul {
        li {
          .logo-container {
            display: flex !important;
            padding: 0px !important;
          }
        }
      }
    }
  }
}


.alignleft {
  float: left;
}

.alignRight {
  float: right;
}

.width-100{
  width: 100%;
}

.toggle-button {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  width: 100%;
  align-items: center;
}

.filter-area {
  display: flex;
  flex-wrap: wrap;
  padding-left: 13px;
  margin-top: 5px;
}

.flexdiv-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding-right: 12px;
}

.jira-button-margin-right {
  margin-right: 10px !important;
}

.refreshedTime{
	width: 40%;
	display: flex;
	align-items: center;
	justify-content: end;
	padding: 8px;
}

.d-inline-block{
  display: inline-block;
}

.mb-0 {
	margin-bottom: 0px !important;
}

.font-bold {
  font-weight: 700 !important;
}

.text-blue-color {
  color: #6896ca;
}

.text-right {
	text-align: right;
}

.cursor-pointer {
  cursor: pointer
}

.cursor-not-allowed {
  cursor: not-allowed
}

.justify-content-end {
  justify-content: end;
}

.gfk-nav nav .nav-list .app-name {
  margin-bottom: 0px !important;
}

.gfk-nav .nav-wrapper,.gfk-section {
  font-size: 14px;
  border-bottom: 1px solid #dfe2e5 !important;
}

th.mat-header-cell {
  color: white !important;
  padding: 0px 24px !important;
  border-right: 1px solid white;
}

td.mat-cell {
  padding: 0px 24px !important;
}

table.mat-table {
  table-layout: fixed;
  width: 100%;
}

.mat-sort-header-arrow {
  color: rgb(255, 255, 255);
}

.mat-sort-header-content{
  width: 100%;
}

.notification-header {
  padding: 30px 0px;
}

.notification-overlay {
  position: fixed !important;
}

.error-text {
  color: gfk-style.$red-400;
  margin-top: 5px;
}

.radio-group_label {
  margin-bottom: 10px !important;
}

.theme-text {
  color: gfk-style.$brand;
}

.transparent-background-theme-btn {
  background-color: gfk-style.$white !important;
  color: gfk-style.$brand !important;
  border: 2px solid gfk-style.$brand !important;
  border-radius: 5px !important;
  font-weight: 700 !important;
}

.transparent-background-theme-btn:hover {
  background-color: gfk-style.$white !important;
}

.gfk-badge {
  padding: 0.5px 4px !important
}

.gfk-badge_soft-action {
  color: #e55a00 !important;
  border: none !important;
  font-size: 14px !important;
  background-color: white !important;
}

.filter-area {
  display: flex;
  flex-wrap: wrap;
  padding-left: 22px;
}

.mat-column-productGroups {
  .gfk-badge_soft-action {
    color: #e55a00 !important;
    border: none !important;
    font-size: 14px !important;
    background-color: white !important;
  }
  .gfk-badge_content {
    font-weight: 700 !important;
  }
}

.gfk-banner {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 999;
}

.gfk-main-100 {
  padding-bottom: 100px;
}

.gfk-main-130 {
  padding-bottom: 130px;
}

.selected-base-project {
  .gfk-chip-container {
    max-height: 150px;
    overflow-y: auto;
    .gfk-chip {
      margin: 10px 10px 10px 0px !important;
    }
  }
}

.tooltip_content {
  word-wrap: break-word; 
}

.gfk-modal-container {
  position: relative;
}

.gfk-modal-container_content {
  min-height: auto !important;
  height: auto !important;
}

.gfk-modal-container_title h3 {
  margin: 0px !important;
}

.header-spacing {
  .gfk-button.primary {
    height: 30px !important;
    font-size: 14px !important;
  }
}

.alpha-banner {
  justify-content: space-between !important;
  .alpha-banner_default {
    flex-basis: auto !important;
  }
  .alpha-banner_actions {
    flex-basis: auto !important;
    align-items: center !important;
  }
}

.sidebar-container {
  width: 100% !important;
  .sidebar-header {
    padding: 18px 16px !important;
  }
  .sidebar-content {
    overflow-y: scroll;
    height: calc(100vh - 170px);
  }
  .sidebar-title {
    line-height: normal;
    color: #9499a3 !important;
    font-size: 32px;
    font-weight: normal !important;
  }
  .content-spacing {
    padding: 0px 16px 1px 1px;
    overflow-y: scroll;
    height: calc(98vh - 160px);
  }
  .sidebar-heading {
    line-height: normal;
    color: #9499a3!important;
    font-size: 32px !important;
    font-weight: 400!important;
    margin: 0px
  }
}

.bp-listing {
  .pages {
    span {
      width: auto !important;
      padding: 5px !important;
      min-width: 2rem !important
    }
  }
  .sidebar-modal-color-without-banner {
    color: #93979c !important;
  }
  .sidebar-modal-color-with-banner {
    color: #803f21 !important;
  }
  .sidebar-container {
    color: black !important;
  }
}

.user-role-options {
	.radio {
		margin-bottom: 20px !important;
	}
}

.empty-screen-height {
	height: calc(100vh - 35vh);
}

.empty-screen-alert {
	display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
	text-align: center;
  color: #9499a3;
}

:focus-visible {
  outline: white;
}

.mt-3 {
  button {
    height: 0px !important;
    padding: 0px !important;
  }
}

.rs-listing {
  .mat-form-field-wrapper {
    margin-top: 0px !important;
  }
  
  .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.5em 0 !important;
  }
  
  .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: white !important;
  }
  
  .mat-form-field-appearance-outline .mat-form-field-wrapper {
    margin: 0px !important;
  }
  
  .mat-form-field-wrapper {
    padding-bottom: 0px !important;
  }
}

.max-height-60vh {
  max-height: 60vh !important;
}