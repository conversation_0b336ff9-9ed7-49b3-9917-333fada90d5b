import axios from 'axios';
import { injectable } from 'inversify';
import { log } from '@gfk/bff/src/lib/logger';
import { ServiceNames, UrmOptions, mainContainer } from '@gfk/bff';
import { OpaClient } from './opa-client';
import { Config } from '../config.type';

@injectable()
class OpaHttpClient implements OpaClient {
  private options: UrmOptions;

  constructor() {
    this.options = mainContainer.get<Config>(ServiceNames.Config).opa;
  }

  async authenticateApis(userRole: string, apiUrl: string, apiMethod: string): Promise<boolean> {

    log.debug({ metaData: { class: 'OpaHttpClient', method: 'authenticateApis' }, message: 'started' });
    
    const payload = {
      "input" : {
        "role": userRole,
        "action": apiMethod,
        "endpoint": apiUrl
      }
    }
    
    const uri = `${this.options.baseUri}`;

    const response = await axios.post<boolean>(uri, payload);

    log.debug({ metaData: { class: 'OpaHttpClient', method: 'authenticateApis' }, message: response.data });

    return Promise.resolve(response.data);
  }
}

export default OpaHttpClient;
