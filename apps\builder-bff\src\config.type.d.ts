import { ExceptionHandlerOptions } from '../middleware/exception-handler/exception-handler-options';
import { BaseProxy, DomainProxy, MapProxy } from '../middleware/proxy/proxy-options';
import { CookieOptions } from '../cookie';
import { IdentityServerOptions } from '../auth/identity-server-options';
import { HealthCheckOptions } from '../health-check/health-check-options';
import { TracingOptions } from '../tracing';
import { MetricOptions } from '../metric-collector';
import { UrmOptions } from '../auth/user-enrichment';
export type Environment = 'production' | 'local';
export declare const CONFIG_KEY = "Config";
export interface Config {
    environment: Environment;
    cookie: CookieOptions;
    proxy: (BaseProxy | MapProxy | DomainProxy)[];
    identityServer: IdentityServerOptions;
    urm: UrmOptions;
    exception: ExceptionHandlerOptions;
    health: HealthCheckOptions;
    tracing: TracingOptions;
    metric: MetricOptions;
    userRoleManagement: UrmOptions;
    opa: UrmOptions;
}
export interface ProcessVariables {
    NODE_ENV?: Environment;
}
