import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { RetailerSeparationListComponent } from './retailer-separation-list.component';

describe('RetailerSeparationListComponent', () => {
  
  const postApiServiceMock = {
    getBaseProjectsListForFilter: jest.fn().mockReturnValue(of({})),
    addRetailerSeparation: jest.fn().mockReturnValue(of([]))
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule],
      declarations: [RetailerSeparationListComponent],
      providers: [
        FormBuilder,
        { provide: PostApiService, useValue: postApiServiceMock },
      ],
    }).compileComponents();
  });

  it('should create', () => {
    expect(RetailerSeparationListComponent).toBeTruthy();
  });

  // Add more test cases here...
});

