import { OverridableTypes, UserEnrichment } from '@gfk/bff';
import { Container } from 'inversify';
import BuilderUserEnrichment from './builder-user-enrichment';
import UserRoleHttpClient from './user-role/user-role-http-client';
import { BuilderXServicesNames } from './di/inversion';
import OpaHttpClient from './opa/opa-http-client';
import { UserRoleClient } from './user-role/user-role-client';
import { OpaClient } from './opa/opa-client';

export const customServiceRegistry = async (container: Container, overridable: OverridableTypes) => {
  (await container.rebind<UserEnrichment>(overridable.UserEnrichment)).to(BuilderUserEnrichment);
  container.bind<UserRoleClient>(BuilderXServicesNames.UserRoleClient).to(UserRoleHttpClient);
  container.bind<OpaClient>(BuilderXServicesNames.OpaClient).to(OpaHttpClient);
};