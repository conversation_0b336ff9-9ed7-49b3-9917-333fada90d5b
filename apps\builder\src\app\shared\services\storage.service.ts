import { Injectable } from '@angular/core';

@Injectable({
	providedIn: 'root',
})
export class StorageService {
	get<T>(key: string): T | null {
		const value = localStorage.getItem(key);
		if (value == null) return null;

		return JSON.parse(value) as T;
	}

	set<T>(key: string, value: T) {
		localStorage.setItem(key, JSON.stringify(value));
	}

	remove(key: string) {
		localStorage.removeItem(key);
	}
}
