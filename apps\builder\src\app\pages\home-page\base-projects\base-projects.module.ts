import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BaseProjectsListComponent } from './base-projects-list/base-projects-list.component';
import { BaseProjectsAddComponent } from './base-projects-add/base-projects-add.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '@builder/shared/shared.module';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSortModule } from '@angular/material/sort';
import { NgLibModule } from '@gfk/ng-lib';
import { DxLibModule } from '@dwh/dx-lib/src';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CanDeactivateGuard } from '@builder/core/guard/can-deactivate.guard';
import { BaseProjectsSecurityComponent } from './base-projects-security/base-projects-security.component';

const routes: Routes = [
	{
		path: "",
		children: [
			{
				path: "list",
				component: BaseProjectsListComponent,
				canActivate: [],
				canDeactivate: []
			},
			{
				path: "create",
				component: BaseProjectsAddComponent,
				canActivate: [],
				canDeactivate: []
			},
			{
				path: "update/:id",
				component: BaseProjectsAddComponent,
				canActivate: [],
				canDeactivate: [CanDeactivateGuard]
			},
			{
				path: "copy/:id",
				component: BaseProjectsAddComponent,
				canActivate: [],
				canDeactivate: [CanDeactivateGuard]
			},

		]
	  }
  ];


@NgModule({
	declarations: [BaseProjectsListComponent, BaseProjectsAddComponent, BaseProjectsSecurityComponent],
	imports: [
		CommonModule,
		SharedModule,
		RouterModule.forChild(routes),
		MatPaginatorModule,
		MatSortModule,
		MatAutocompleteModule,
		NgLibModule,
		DxLibModule,
		FormsModule,
		ReactiveFormsModule
	],
	exports: [RouterModule],
	schemas: [
		CUSTOM_ELEMENTS_SCHEMA
	]
})
export class BaseProjectsModule {}
