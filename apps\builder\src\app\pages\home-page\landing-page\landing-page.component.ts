import { Component, HostListener, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { ConfigService } from '@builder/shared/services/config.service';
import { Subject, Subscription } from 'rxjs';
import { NavigationEnd, Router } from '@angular/router';
import { EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';
import { BannerService } from '@builder/shared/services/banner.service';
import { GetApiService } from '@builder/shared/services/get-api.service';

interface ICard {
	title: string;
	description: string;
  url: string;
	tooltip:string;
	disableEnableButton:boolean;
}

@Component({
  selector: 'dx-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss'],
  providers: [EdsNotificationService],
})
export class LandingPageComponent implements OnInit, OnDestroy {
  
  private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);
  private readonly bannerService = inject(BannerService);
  LandingPageURL = "/https://home.de.vpc1445.in.gfk.com";
  TestApp = 'DWH Builder X';
  withProfile = true;
  withSection = false;
  version?: string;
  currentTab = window.location.href.split('/')[3];
  loginDisplay = false;

  private readonly _destroying$ = new Subject<void>();
  event$ 
  hideBanner: any;
  isBannerVisible!: boolean;
  displayUI = false;
  cards: ICard[]= [
    {
      title: "Base Projects",
	    description: `Also includes QC Projects and QC Periods`,
      url: "/base-projects/list",
      disableEnableButton:false,
      tooltip: "",
     },
     {
      title: "Retailer Separation",
	    description: `Separation of I/R`,
      url: "/retailer-separation/list",
      disableEnableButton:false,
      tooltip: "",
     },
     {
      title: "Production Projects",
	    description: `Also include Data Orders`,
	    url: "/coverage-imports",
      disableEnableButton:true,
      tooltip: "",
     },
    //  {
    //   title: "Production Projects",
	  //   description: `Also include Data Orders`,
	  //   url: "/coverage-imports",
    //   disableEnableButton:true,
    //   tooltip: "",
    //  }
  ];
  emptyUserPermission = false;
  showContent!: boolean;
  subscription!: Subscription;

  constructor(
    private configService: ConfigService, 
    public router: Router,
    private getApiService: GetApiService
  ) {
    const packageVersion = this.configService.getVersion();
    this.version = packageVersion.substring(packageVersion.indexOf('_') + 1);
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.getMarginTop(0);
  }


  tabActive(activetab: string) {
    if (this.currentTab == activetab) {
      this.currentTab = '';
    } else {
      this.currentTab = activetab;
    }
  }

  getMarginTop(number) {
    return number + 25 +'px';
  }

  ngOnInit(): void {
    this.router.events.subscribe((e) => {
			if (e instanceof NavigationEnd) {
			  if(!e.url.includes('redirect-web')){
				this.showContent = true;
			  }
			}
		});
    setTimeout(() => {
      this.displayUI = true;
    }, 1000);
    this.bannerService.isVisible$.subscribe((response: any) => {
      this.isBannerVisible = response;
    });
    this.tabActive(this.currentTab);
    this.subscription = this.getApiService.getUserRoleSubject().subscribe((userRole) => {
      if(userRole.length == 1){
        userRole.forEach((item: any) => {
          if(item.status == 'Approved'){
            this.emptyUserPermission = false;
          }
          else{
            this.emptyUserPermission = true;
          }
        });
      }
      else if(userRole.length > 1){
        this.emptyUserPermission = false;
      }
      else{
        this.emptyUserPermission = true;
      }
    });
  }

  getLoggedInUserRole(){
		this.getApiService.getLoggedInUserRole()
		.subscribe({
			next: (result: any) => {
        if(result.length == 1){
					result.forEach((item: any) => {
						if(item.status == 'Approved'){
							this.emptyUserPermission = false;
						}
						else{
							this.emptyUserPermission = true;
						}
					});
				}
        else if(result.length > 1){
          this.emptyUserPermission = false;
        }
        else{
          this.emptyUserPermission = true;
        }
			},
			error: (error) => {
				let title: string;
				if(error.status == 400 || error.status == 401 || error.status == 500 || error.status == 502 || error.status == 503 || error.status == 504){
					title = 'Error: ' + error.status + '. Unable to fetch User Role. Please contact administrator.';
					this.notifyWidget(title, 'error');
				}
				else {
					if(error.status == 404){
						this.emptyUserPermission = true;
					}
				}
			},
		})
	}

  ngOnDestroy(): void {
    this._destroying$.next(undefined);
    this._destroying$.complete();
  }

  notifyWidget(title: string, notificationType: string, message?: string): void {
		if(notificationType == 'info'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if(notificationType == 'error'){
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else{
			this.notificationService.showNotification(title, {
				message: message || '',
				duration : 15000,
				variant: NotificationVariant.SUCCESS,
			});
		}
	}
}
