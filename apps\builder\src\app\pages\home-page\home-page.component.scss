// Example usage of gfk theme variables:
@use 'node_modules/@angular/material' as mat;
@use 'sass:map' as map;
@use '../../../styles/theme/gfk-light.palette' as gfk;
@use '../../../styles/theme/material-gfk.typography' as type;
@use '../../../styles/theme/material-gfk-light.palette' as mat-colors;
@use '../../../styles/theme/gfk-light.palette' as colors;
@use '@gfk/style' as gfk-style;

$config: mat.get-color-config(mat-colors.$theme);
$background: map-get($config, background);
$foreground: map-get($config, foreground);

%custom-component-selector {
  .something-that-looks-like-h2 {
    //@include mat.typography-level($gfk-typography, 'title');
    //or
    font-size: mat.font-size(type.$typography, 'title');
    font-family: mat.font-family(type.$typography, 'title');
    font-weight: mat.font-weight(type.$typography, 'title');
    line-height: mat.line-height(type.$typography, 'title');
    color: mat.get-color-from-palette($foreground, text);
    background: mat.get-color-from-palette($background, 'background');
  }
}

.header-spacing {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.heading {
	border-top: 0px solid rgb(223 226 229);
	border-bottom: 1px solid rgb(223 226 229);
	padding: 10px 0px;
}