import { TestBed } from '@angular/core/testing';

import { DeleteApiService } from './delete-api.service';
import { HttpClient, HttpHandler } from '@angular/common/http';

describe('DeleteApiService', () => {
  let service: DeleteApiService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        HttpClient,
        HttpHandler
      ]
    });
    service = TestBed.inject(DeleteApiService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
