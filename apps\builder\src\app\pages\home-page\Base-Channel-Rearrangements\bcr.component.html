<div class="relative-position bp-listing" [ngClass]="(bcrDataNotFound) ? 'empty-screen-height' : ''">
  <div class="empty-screen-alert" *ngIf="bcrDataNotFound">
    <div>
      <h3>
  <svg width="120px" height="120px" viewBox="0 0 48 48" version="1" xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 48 48">
      <circle fill="#00ACC1" cx="17" cy="17" r="14"/>
      <circle fill="#eee" cx="17" cy="17" r="11"/>
      <rect x="16" y="8" width="2" height="9"/>
      <rect x="18.2" y="16" transform="matrix(-.707 .707 -.707 -.707 46.834 19.399)" width="2.4" height="6.8"/>
      <circle cx="17" cy="17" r="2"/>
      <circle fill="#00ACC1" cx="17" cy="17" r="1"/>
      <path fill="#FFC107" d="M11.9,42l14.4-24.1c0.8-1.3,2.7-1.3,3.4,0L44.1,42c0.8,1.3-0.2,3-1.7,3H13.6C12.1,45,11.1,43.3,11.9,42z"/>
      <path fill="#263238" d="M26.4,39.9c0-0.2,0-0.4,0.1-0.6s0.2-0.3,0.3-0.5s0.3-0.2,0.5-0.3s0.4-0.1,0.6-0.1s0.5,0,0.7,0.1 s0.4,0.2,0.5,0.3s0.2,0.3,0.3,0.5s0.1,0.4,0.1,0.6s0,0.4-0.1,0.6s-0.2,0.3-0.3,0.5s-0.3,0.2-0.5,0.3s-0.4,0.1-0.7,0.1 s-0.5,0-0.6-0.1s-0.4-0.2-0.5-0.3s-0.2-0.3-0.3-0.5S26.4,40.1,26.4,39.9z M29.2,36.8h-2.3L26.5,27h3L29.2,36.8z"/>
  </svg>
        <br><b>Link Expired</b><br><br>
        The link has expired. Please contact the owner of this link to get a new one.
      </h3>
    </div>
  </div>
</div>


  <div class="bcr-details-container" *ngIf="bcrData">
    <h2>BCR Conflicts</h2>
    <div *ngIf="bcrData; else loading">
      <div class="mx-6">
        <article class="table-grid mt-5">
          <eds-table eds-id="bcr-conflicts-table">
            <thead slot="thead">
              <eds-tr variant="header">
                <eds-th column-index="1">BCR ID</eds-th>
                <eds-th column-index="2">Product Group</eds-th>
                <eds-th column-index="3">Source Channel</eds-th>
                <eds-th column-index="4">Feature / Feature Value</eds-th>
                <eds-th column-index="5">Outlet ID</eds-th>
                <eds-th column-index="6">Target Channel</eds-th>
                <eds-th column-index="7">From Period</eds-th>
                <eds-th column-index="8">To Period</eds-th>
                <eds-th column-index="9">Message</eds-th>
              </eds-tr>
            </thead>
            <tbody slot="tbody">
              <eds-tr
                *ngFor="let detail of visibleBCRDetailList; let i = index; trackBy: trackByIndex"
                [rowIndex]="i"
              >
                <eds-td>{{ detail?.baseChannelRearrangementId }}</eds-td>
                <eds-td>{{ detail?.productGroup }}</eds-td>
                <eds-td>{{ detail?.channel }}</eds-td>
                <eds-td>
                  {{ detail?.feature }} / {{ detail?.featureValue }}
                </eds-td>
                <eds-td>{{ detail?.outletId }}</eds-td>
                <eds-td>{{ detail?.baseChannel }}</eds-td>
                <eds-td>{{ detail?.fromPeriodId }}</eds-td>
                <eds-td>{{ detail?.toPeriodId }}</eds-td>
                <eds-td>{{ detail?.message }}</eds-td>
              </eds-tr>
            </tbody>
          </eds-table>
          <gfk-pagination
            *ngIf="bcrDetailList?.length > 0 && bcrDetailList?.length > defaultPageSizeforBCRDetail"
            id="pagination-create-ld"
            [itemsPerPageOptions]="pageSizeOptionsForBCRDetail"
            [totalCount]="bcrDetailList?.length"
            [position]="'right'"
            [showItemsPerPage]="true"
            [showFirstAndLast]="true"
            [defaultPageSize]="defaultPageSizeforBCRDetail"
            [currentPage]="currentPageForBCRDetail"
            (onPage)="onPageChangeBCRDetail($event)">
          </gfk-pagination>
        </article>
      </div>
    </div>
    <ng-template #loading>
      <p>Loading BCR details...</p>
    </ng-template>
  </div>


