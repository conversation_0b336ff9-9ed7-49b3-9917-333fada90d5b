import { ChangeDetectorRef, Component, computed, QueryList, signal, ViewChildren } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EdsTr, NgLibModule } from '@gfk/ng-lib';
import { HasMultiSelectTable } from './has-multi-select-table';
import { EntityId } from '../../../../../../../libs/dx-lib/src/lib/interfaces/entity-id';

interface Person {
	id: EntityId;
	name: string;
	age: number;
}
@Component({
	selector: 'dx-multi-select-table',
	standalone: true,
	imports: [NgLibModule],
	template: `
		@if (data(); as persons) {
			<eds-table eds-id="test-table" has-row-select="checkbox" (edsRowSelected)="onSelect($event, allFetchedProductGroups())">
				<thead slot="thead">
					<eds-tr variant="header">
						<eds-th variant="'selector'" columnIndex="0" [isSelected]="areAllSelected(persons)">Id</eds-th>
						<eds-th column-index="1" sort="asc">Age</eds-th>
						<eds-th column-index="1" sort="asc">Name</eds-th>
					</eds-tr>
					<eds-tr slot="thead" variant="aggregated" row-index="0">
						<eds-td
							id="select-all-cell"
							colspan="3"
							variant="selector"
							[isSelected]="areAllSelected(persons.length)"
							(toggleRowSelect)="onSelectAll($event, persons)">
							{{ persons.length }} Persons
						</eds-td>
					</eds-tr>
				</thead>
				<tbody slot="tbody">
					@for (person of persons; track person.id) {
						<eds-tr [rowIndex]="person.id">
							<eds-td [isSelected]="isSelected(person.id)" variant="selector">
								{{ person.id }}
							</eds-td>
							<eds-td>{{ person.age }}</eds-td>
							<eds-td>{{ person.name }}</eds-td>
						</eds-tr>
					} @empty {
						<eds-tr><eds-td colspan="3">No Data available.</eds-td></eds-tr>
					}
				</tbody>
			</eds-table>
		}
	`,
})
class MultiSelectTableComponent extends HasMultiSelectTable<Person> {
	data = signal<Person[]>([]);
	@ViewChildren(EdsTr) _allRows!: QueryList<EdsTr>;
	allRows = computed(() => this.filterBodyRows(this._allRows.toArray()));
	constructor(cdRef: ChangeDetectorRef) {
		super(cdRef);
	}
	
	getId({ id }: Person): EntityId {
		return id;
	}

	setData(data: Person[]): void {
		this.data.set(data);
	}
}
describe('HasMultiSelectTable', () => {
	let component: MultiSelectTableComponent;
	let fixture: ComponentFixture<MultiSelectTableComponent>;
	const testData = Array.from({ length: 25 }, (_, index) => ({
		id: index + 1,
		name: `Person ${index + 1}`,
		age: Math.floor(Math.random() * 50) + 18,
	}));

	beforeEach(async () => {
		await TestBed.configureTestingModule({ imports: [MultiSelectTableComponent] }).compileComponents();
		fixture = TestBed.createComponent(MultiSelectTableComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('with data', () => {
		beforeEach(async () => {
			component.setData(testData);
			fixture.detectChanges();
		});

		it('should create', () => {
			expect(component).toBeTruthy();
		});

		it('should show table', () => {
			expect(fixture.nativeElement.querySelector('eds-table')).toBeTruthy();
		});

		describe('rows', () => {
			it('should have all rows', () => {
				expect(fixture.nativeElement.querySelectorAll('eds-tr').length).toBe(27);
			});
			it('should have all body rows', () => {
				expect(component.allRows().length).toBe(25);
			});
		});
	});
});
